﻿@inherits LayoutComponentBase
@using CodingZoo.Client.Services
@inject IThemeService ThemeService
@inject IJSRuntime JSRuntime

<div class="min-h-screen bg-white dark:bg-gray-900 transition-colors duration-300">
    <!-- Navigation Header -->
    <header class="sticky top-0 z-50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="/" class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-gradient-to-r from-primary-600 to-accent-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-lg">CZ</span>
                        </div>
                        <span class="text-xl font-bold gradient-text">CodingZoo</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="/" class="nav-link">Home</a>
                    <a href="/articles" class="nav-link">Articles</a>
                    <a href="/categories" class="nav-link">Categories</a>
                    <a href="/about" class="nav-link">About</a>
                    <a href="/contact" class="nav-link">Contact</a>
                </nav>

                <!-- Right Side Actions -->
                <div class="flex items-center space-x-4">
                    <!-- Search Button -->
                    <button class="btn-ghost p-2" @onclick="OpenSearch">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </button>

                    <!-- Theme Toggle -->
                    <button class="btn-ghost p-2" @onclick="ToggleTheme">
                        @if (isDarkMode)
                        {
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                            </svg>
                        }
                        else
                        {
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                            </svg>
                        }
                    </button>

                    <!-- User Menu / Login -->
                    <div class="relative">
                        <button class="btn-primary">
                            Sign In
                        </button>
                    </div>

                    <!-- Mobile Menu Button -->
                    <button class="md:hidden btn-ghost p-2" @onclick="ToggleMobileMenu">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        @if (showMobileMenu)
        {
            <div class="md:hidden border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
                <div class="px-2 pt-2 pb-3 space-y-1">
                    <a href="/" class="nav-link block">Home</a>
                    <a href="/articles" class="nav-link block">Articles</a>
                    <a href="/categories" class="nav-link block">Categories</a>
                    <a href="/about" class="nav-link block">About</a>
                    <a href="/contact" class="nav-link block">Contact</a>
                </div>
            </div>
        }
    </header>

    <!-- Main Content -->
    <main class="flex-1">
        @Body
    </main>

    <!-- Footer -->
    <footer class="bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Brand -->
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-8 h-8 bg-gradient-to-r from-primary-600 to-accent-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-lg">CZ</span>
                        </div>
                        <span class="text-xl font-bold gradient-text">CodingZoo</span>
                    </div>
                    <p class="text-gray-600 dark:text-gray-400 mb-4">
                        Your Ultimate Coding Education Platform. Learn programming languages, frameworks, and best practices with our comprehensive tutorials and guides.
                    </p>
                    <p class="text-sm text-gray-500 dark:text-gray-500">
                        Powered by <span class="font-semibold text-primary-600 dark:text-primary-400">ArslanDevs</span>
                    </p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100 uppercase tracking-wider mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="/articles" class="footer-link">Articles</a></li>
                        <li><a href="/categories" class="footer-link">Categories</a></li>
                        <li><a href="/about" class="footer-link">About Us</a></li>
                        <li><a href="/contact" class="footer-link">Contact</a></li>
                    </ul>
                </div>

                <!-- Categories -->
                <div>
                    <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100 uppercase tracking-wider mb-4">Popular</h3>
                    <ul class="space-y-2">
                        <li><a href="/category/csharp" class="footer-link">C#</a></li>
                        <li><a href="/category/python" class="footer-link">Python</a></li>
                        <li><a href="/category/javascript" class="footer-link">JavaScript</a></li>
                        <li><a href="/category/react" class="footer-link">React</a></li>
                    </ul>
                </div>
            </div>

            <div class="mt-8 pt-8 border-t border-gray-200 dark:border-gray-700">
                <p class="text-center text-gray-500 dark:text-gray-400 text-sm">
                    © @DateTime.Now.Year CodingZoo. All rights reserved. Built with ❤️ by ArslanDevs.
                </p>
            </div>
        </div>
    </footer>
</div>
