# CodingZoo - Your Ultimate Coding Education Platform

![CodingZoo Logo](https://via.placeholder.com/800x200/3B82F6/FFFFFF?text=CodingZoo+-+Powered+by+ArslanDevs)

CodingZoo is a modern, full-featured coding education website built with B<PERSON>zor WebAssembly, ASP.NET Core, and Tailwind CSS. It provides comprehensive programming tutorials, guides, and resources for developers of all skill levels.

## 🚀 Features

### Frontend (Blazor WebAssembly)
- **Modern UI/UX**: Clean, responsive design with Tailwind CSS
- **Dark/Light Mode**: Seamless theme switching with system preference detection
- **Interactive Components**: Smooth animations and micro-interactions
- **Lazy Loading**: Optimized content loading for better performance
- **Progressive Web App**: PWA capabilities with offline support
- **Responsive Design**: Mobile-first approach with perfect tablet and desktop layouts

### Backend (ASP.NET Core Web API)
- **RESTful API**: Clean, well-documented API endpoints
- **JWT Authentication**: Secure user authentication and authorization
- **Entity Framework Core**: Code-first database approach with SQL Server
- **Repository Pattern**: Clean architecture with separation of concerns
- **AutoMapper**: Efficient object-to-object mapping
- **Swagger Documentation**: Interactive API documentation

### Content Management
- **WordPress-style Admin Panel**: Intuitive content management interface
- **Rich Text Editor**: WYSIWYG editor with Markdown support
- **Media Management**: Image upload with drag-drop functionality
- **SEO Optimization**: Meta tags, slugs, and search engine optimization
- **Article Scheduling**: Publish articles immediately or schedule for later

### User Features
- **User Registration/Login**: Secure authentication system
- **Article Bookmarking**: Save favorite articles for later reading
- **Comment System**: Engage with the community through comments
- **Search Functionality**: Powerful search with filtering and sorting
- **Category Browsing**: Organized content by programming languages and topics

## 🛠 Technology Stack

### Frontend
- **Blazor WebAssembly** - Client-side web framework
- **Tailwind CSS** - Utility-first CSS framework
- **JavaScript Interop** - For browser APIs and third-party libraries

### Backend
- **ASP.NET Core 9.0** - Web API framework
- **Entity Framework Core** - ORM for database operations
- **SQL Server** - Primary database
- **JWT Bearer Authentication** - Secure token-based authentication
- **AutoMapper** - Object mapping
- **BCrypt.Net** - Password hashing

### Development Tools
- **Visual Studio 2022** - Primary IDE
- **Node.js & npm** - For Tailwind CSS build process
- **Swagger/OpenAPI** - API documentation
- **Git** - Version control

## 📁 Project Structure

```
CodingZoo/
├── src/
│   ├── CodingZoo.Client/          # Blazor WebAssembly frontend
│   │   ├── Components/            # Reusable UI components
│   │   ├── Layout/               # Layout components
│   │   ├── Pages/                # Page components
│   │   ├── Services/             # HTTP client services
│   │   └── wwwroot/              # Static assets
│   ├── CodingZoo.API/            # ASP.NET Core Web API
│   │   ├── Controllers/          # API controllers
│   │   ├── Data/                 # Database context
│   │   ├── Repositories/         # Data access layer
│   │   ├── Services/             # Business logic
│   │   └── Mappings/             # AutoMapper profiles
│   └── CodingZoo.Shared/         # Shared models and DTOs
│       ├── Models/               # Entity models
│       ├── DTOs/                 # Data transfer objects
│       ├── Common/               # Common utilities
│       └── Constants/            # Application constants
├── CodingZoo.sln                 # Solution file
└── README.md                     # This file
```

## 🚀 Getting Started

### Prerequisites
- [.NET 9.0 SDK](https://dotnet.microsoft.com/download/dotnet/9.0)
- [Node.js](https://nodejs.org/) (for Tailwind CSS)
- [SQL Server](https://www.microsoft.com/en-us/sql-server/sql-server-downloads) or SQL Server LocalDB
- [Visual Studio 2022](https://visualstudio.microsoft.com/) or [VS Code](https://code.visualstudio.com/)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/codingzoo.git
   cd codingzoo
   ```

2. **Install .NET dependencies**
   ```bash
   dotnet restore
   ```

3. **Install Node.js dependencies (for Tailwind CSS)**
   ```bash
   cd src/CodingZoo.Client
   npm install
   ```

4. **Configure the database connection**
   
   Update the connection string in `src/CodingZoo.API/appsettings.json`:
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=CodingZooDb;Trusted_Connection=true;MultipleActiveResultSets=true"
     }
   }
   ```

5. **Create and seed the database**
   ```bash
   cd src/CodingZoo.API
   dotnet ef database update
   ```

6. **Build Tailwind CSS**
   ```bash
   cd src/CodingZoo.Client
   npm run build-css
   ```

### Running the Application

1. **Start the API server**
   ```bash
   cd src/CodingZoo.API
   dotnet run
   ```
   The API will be available at `https://localhost:7000`

2. **Start the Blazor client** (in a new terminal)
   ```bash
   cd src/CodingZoo.Client
   dotnet run
   ```
   The client will be available at `https://localhost:7001`

3. **Watch Tailwind CSS changes** (optional, in a new terminal)
   ```bash
   cd src/CodingZoo.Client
   npm run build-css
   ```

## 🎨 Design System

CodingZoo uses a comprehensive design system built with Tailwind CSS:

### Color Palette
- **Primary**: Blue shades for main actions and branding
- **Secondary**: Gray shades for text and backgrounds
- **Accent**: Purple shades for highlights and special elements
- **Success**: Green for positive actions
- **Warning**: Yellow for cautions
- **Error**: Red for errors and destructive actions

### Components
- **Buttons**: Primary, secondary, outline, and ghost variants
- **Cards**: Hover effects and consistent spacing
- **Forms**: Styled inputs, textareas, and selects
- **Badges**: Color-coded labels for categories and status
- **Loading States**: Spinners, dots, and skeleton loaders

### Animations
- **Fade In/Out**: Smooth content transitions
- **Slide Animations**: Directional content movement
- **Hover Effects**: Interactive element feedback
- **Loading Animations**: Engaging loading states

## 🔧 Configuration

### JWT Settings
Configure JWT authentication in `appsettings.json`:
```json
{
  "Jwt": {
    "SecretKey": "your-super-secret-key-here",
    "Issuer": "CodingZoo.API",
    "Audience": "CodingZoo.Client",
    "ExpirationMinutes": 60
  }
}
```

### CORS Settings
The API is configured to allow requests from the Blazor client. Update the CORS policy in `Program.cs` if needed.

## 📚 API Documentation

Once the API is running, visit `https://localhost:7000/swagger` to explore the interactive API documentation.

### Key Endpoints
- `GET /api/articles` - Get paginated articles with filtering
- `GET /api/articles/{id}` - Get article by ID
- `GET /api/categories` - Get all categories
- `POST /api/auth/login` - User authentication
- `POST /api/auth/register` - User registration

## 🧪 Testing

### Running Tests
```bash
# Run all tests
dotnet test

# Run tests with coverage
dotnet test --collect:"XPlat Code Coverage"
```

### Test Structure
- **Unit Tests**: Test individual components and services
- **Integration Tests**: Test API endpoints and database operations
- **E2E Tests**: Test complete user workflows

## 🚀 Deployment

### Production Build
```bash
# Build the API
cd src/CodingZoo.API
dotnet publish -c Release -o ./publish

# Build the client
cd src/CodingZoo.Client
npm run build-css-prod
dotnet publish -c Release -o ./publish
```

### Docker Support
```dockerfile
# Example Dockerfile for the API
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src
COPY ["src/CodingZoo.API/CodingZoo.API.csproj", "src/CodingZoo.API/"]
RUN dotnet restore "src/CodingZoo.API/CodingZoo.API.csproj"
COPY . .
WORKDIR "/src/src/CodingZoo.API"
RUN dotnet build "CodingZoo.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "CodingZoo.API.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "CodingZoo.API.dll"]
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **ArslanDevs** - Development and architecture
- **Tailwind CSS** - For the amazing utility-first CSS framework
- **Microsoft** - For the excellent .NET ecosystem
- **DevIcon** - For the programming language icons

## 📞 Support

For support, email <EMAIL> or join our [Discord community](https://discord.gg/codingzoo).

---

**CodingZoo** - Empowering developers worldwide 🦁
*Powered by ArslanDevs*
