using Microsoft.EntityFrameworkCore;
using CodingZoo.API.Data;
using CodingZoo.Shared.Models;

namespace CodingZoo.API.Repositories;

public class CommentRepository : Repository<Comment>, ICommentRepository
{
    public CommentRepository(CodingZooDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<Comment>> GetCommentsByArticleAsync(int articleId)
    {
        return await _dbSet.Include(c => c.User)
                          .Include(c => c.Replies)
                          .ThenInclude(r => r.User)
                          .Where(c => c.ArticleId == articleId && c.IsApproved && c.ParentCommentId == null)
                          .OrderBy(c => c.CreatedAt)
                          .ToListAsync();
    }

    public async Task<IEnumerable<Comment>> GetPendingCommentsAsync()
    {
        return await _dbSet.Include(c => c.User)
                          .Include(c => c.Article)
                          .Where(c => !c.IsApproved)
                          .OrderByDescending(c => c.CreatedAt)
                          .ToListAsync();
    }

    public async Task ApproveCommentAsync(int commentId)
    {
        var comment = await _dbSet.FindAsync(commentId);
        if (comment != null)
        {
            comment.IsApproved = true;
            await _context.SaveChangesAsync();
        }
    }

    public async Task RejectCommentAsync(int commentId)
    {
        var comment = await _dbSet.FindAsync(commentId);
        if (comment != null)
        {
            await DeleteAsync(comment);
        }
    }
}

public class MediaRepository : Repository<Media>, IMediaRepository
{
    public MediaRepository(CodingZooDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<Media>> GetMediaByUserAsync(int userId)
    {
        return await _dbSet.Include(m => m.UploadedBy)
                          .Where(m => m.UploadedByUserId == userId)
                          .OrderByDescending(m => m.CreatedAt)
                          .ToListAsync();
    }

    public async Task<Media?> GetMediaByFileNameAsync(string fileName)
    {
        return await _dbSet.Include(m => m.UploadedBy)
                          .FirstOrDefaultAsync(m => m.FileName == fileName);
    }
}
