@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');
@import url('https://cdn.jsdelivr.net/gh/devicons/devicon@latest/devicon.min.css');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    html {
        font-family: 'Inter', system-ui, sans-serif;
        scroll-behavior: smooth;
    }

    body {
        @apply bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300;
    }

    /* Custom scrollbar */
    ::-webkit-scrollbar {
        width: 8px;
    }

    ::-webkit-scrollbar-track {
        @apply bg-gray-100 dark:bg-gray-800;
    }

    ::-webkit-scrollbar-thumb {
        @apply bg-gray-300 dark:bg-gray-600 rounded-full;
    }

    ::-webkit-scrollbar-thumb:hover {
        @apply bg-gray-400 dark:bg-gray-500;
    }
}

@layer components {
    /* Button Components */
    .btn {
        @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
    }

    .btn-primary {
        @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 dark:bg-primary-500 dark:hover:bg-primary-600;
    }

    .btn-secondary {
        @apply btn bg-secondary-100 text-secondary-900 hover:bg-secondary-200 focus:ring-secondary-500 dark:bg-secondary-800 dark:text-secondary-100 dark:hover:bg-secondary-700;
    }

    .btn-outline {
        @apply btn border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-primary-500 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800;
    }

    .btn-ghost {
        @apply btn text-gray-600 hover:bg-gray-100 focus:ring-primary-500 dark:text-gray-400 dark:hover:bg-gray-800;
    }

    .btn-sm {
        @apply px-3 py-1.5 text-xs;
    }

    .btn-lg {
        @apply px-6 py-3 text-base;
    }

    /* Card Components */
    .card {
        @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 transition-all duration-200;
    }

    .card-hover {
        @apply card hover:shadow-lg hover:border-gray-300 dark:hover:border-gray-600 hover:-translate-y-1;
    }

    .card-body {
        @apply p-6;
    }

    .card-header {
        @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700;
    }

    .card-footer {
        @apply px-6 py-4 border-t border-gray-200 dark:border-gray-700;
    }

    /* Form Components */
    .form-input {
        @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800 dark:border-gray-600 dark:text-white dark:placeholder-gray-500 dark:focus:ring-primary-400 dark:focus:border-primary-400;
    }

    .form-textarea {
        @apply form-input resize-none;
    }

    .form-select {
        @apply form-input pr-10 bg-no-repeat bg-right;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 0.5rem center;
        background-size: 1.5em 1.5em;
    }

    .form-label {
        @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2;
    }

    .form-error {
        @apply text-sm text-error-600 dark:text-error-400 mt-1;
    }

    /* Badge Components */
    .badge {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    }

    .badge-primary {
        @apply badge bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200;
    }

    .badge-secondary {
        @apply badge bg-secondary-100 text-secondary-800 dark:bg-secondary-900 dark:text-secondary-200;
    }

    .badge-success {
        @apply badge bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200;
    }

    .badge-warning {
        @apply badge bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-200;
    }

    .badge-error {
        @apply badge bg-error-100 text-error-800 dark:bg-error-900 dark:text-error-200;
    }

    /* Loading Components */
    .loading-spinner {
        @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
    }

    .loading-dots {
        @apply flex space-x-1;
    }

    .loading-dot {
        @apply w-2 h-2 bg-primary-600 rounded-full animate-bounce;
    }

    .loading-dot:nth-child(2) {
        animation-delay: 0.1s;
    }

    .loading-dot:nth-child(3) {
        animation-delay: 0.2s;
    }

    /* Skeleton Loading */
    .skeleton {
        @apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded;
    }

    .skeleton-text {
        @apply skeleton h-4 mb-2;
    }

    .skeleton-title {
        @apply skeleton h-6 mb-4;
    }

    .skeleton-avatar {
        @apply skeleton w-10 h-10 rounded-full;
    }

    /* Glass Effect */
    .glass {
        @apply backdrop-blur-md bg-white/10 dark:bg-gray-900/10 border border-white/20 dark:border-gray-700/20;
    }

    /* Gradient Text */
    .gradient-text {
        @apply bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent;
    }

    /* Hero Section */
    .hero-bg {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .hero-bg-dark {
        background: linear-gradient(135deg, #1e3a8a 0%, #4c1d95 100%);
    }

    /* Code Syntax Highlighting */
    .code-block {
        @apply bg-gray-900 text-gray-100 rounded-lg p-4 overflow-x-auto;
    }

    .code-inline {
        @apply bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 px-1.5 py-0.5 rounded text-sm font-mono;
    }

    /* Article Content Styling */
    .prose-custom {
        @apply prose prose-lg dark:prose-invert max-w-none;
    }

    .prose-custom h1,
    .prose-custom h2,
    .prose-custom h3,
    .prose-custom h4,
    .prose-custom h5,
    .prose-custom h6 {
        @apply text-gray-900 dark:text-gray-100 font-bold;
    }

    .prose-custom a {
        @apply text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 no-underline hover:underline;
    }

    .prose-custom blockquote {
        @apply border-l-4 border-primary-500 bg-primary-50 dark:bg-primary-900/20 pl-4 py-2 my-4 italic;
    }

    /* Navigation */
    .nav-link {
        @apply text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200;
    }

    .nav-link-active {
        @apply nav-link text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20;
    }

    /* Footer */
    .footer-link {
        @apply text-gray-400 hover:text-gray-300 transition-colors duration-200;
    }
}

@layer utilities {
    .text-shadow {
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .text-shadow-lg {
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    .bg-pattern {
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }

    .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }
}

/* Blazor Error UI - Keep original styles */
#blazor-error-ui {
    color-scheme: light only;
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    box-sizing: border-box;
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

#blazor-error-ui .dismiss {
    cursor: pointer;
    position: absolute;
    right: 0.75rem;
    top: 0.5rem;
}

.blazor-error-boundary {
    background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNDkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIG92ZXJmbG93PSJoaWRkZW4iPjxkZWZzPjxjbGlwUGF0aCBpZD0iY2xpcDAiPjxyZWN0IHg9IjIzNSIgeT0iNTEiIHdpZHRoPSI1NiIgaGVpZ2h0PSI0OSIvPjwvY2xpcFBhdGg+PC9kZWZzPjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMCkiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMzUgLTUxKSI+PHBhdGggZD0iTTI2My41MDYgNTFDMjY0LjcxNyA1MSAyNjUuODEzIDUxLjQ4MzcgMjY2LjYwNiA1Mi4yNjU4TDI2Ny4wNTIgNTIuNzk4NyAyNjcuNTM5IDUzLjYyODMgMjkwLjE4NSA5Mi4xODMxIDI5MC41NDUgOTIuNzk1IDI5MC42NTYgOTIuOTk2QzI5MC44NzcgOTMuNTEzIDI5MSA5NC4wODE1IDI5MSA5NC42NzgyIDI5MSA5Ny4wNjUxIDI4OS4wMzggOTkgMjg2LjYxNyA5OUwyNDAuMzgzIDk5QzIzNy45NjMgOTkgMjM2IDk3LjA2NTEgMjM2IDk0LjY3ODIgMjM2IDk0LjM3OTkgMjM2LjAzMSA5NC4wODg2IDIzNi4wODkgOTMuODA3MkwyMzYuMzM4IDkzLjAxNjIgMjM2Ljg1OCA5Mi4xMzE0IDI1OS40NzMgNTMuNjI5NCAyNTkuOTYxIDUyLjc5ODUgMjYwLjQwNyA1Mi4yNjU4QzI2MS4yIDUxLjQ4MzcgMjYyLjI5NiA1MSAyNjMuNTA2IDUxWk0yNjMuNTg2IDY2LjAxODNDMjYwLjczNyA2Ni4wMTgzIDI1OS4zMTMgNjcuMTI0NSAyNTkuMzEzIDY5LjMzNyAyNTkuMzEzIDY5LjYxMDIgMjU5LjMzMiA2OS44NjA4IDI1OS4zNzEgNzAuMDg4N0wyNjEuNzk1IDg0LjAxNjEgMjY1LjM4IDg0LjAxNjEgMjY3LjgyMSA2OS43NDc1QzI2Ny44NiA2OS43MzA5IDI2Ny44NzkgNjkuNTg3NyAyNjcuODc5IDY5LjMxNzkgMjY3Ljg3OSA2Ny4xMTgyIDI2Ni40NDggNjYuMDE4MyAyNjMuNTg2IDY2LjAxODNaTTI2My41NzYgODYuMDU0N0MyNjEuMDQ5IDg2LjA1NDcgMjU5Ljc4NiA4Ny4zMDA1IDI1OS43ODYgODkuNzkyMSAyNTkuNzg2IDkyLjI4MzcgMjYxLjA0OSA5My41Mjk1IDI2My41NzYgOTMuNTI5NSAyNjYuMTE2IDkzLjUyOTUgMjY3LjM4NyA5Mi4yODM3IDI2Ny4zODcgODkuNzkyMSAyNjcuMzg3IDg3LjMwMDUgMjY2LjExNiA4Ni4wNTQ3IDI2My41NzYgODYuMDU0N1oiIGZpbGw9IiNGRkU1MDAiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvZz48L3N2Zz4=) no-repeat 1rem/1.8rem, #b32121;
    padding: 1rem 1rem 1rem 3.7rem;
    color: white;
}

.blazor-error-boundary::after {
    content: "An error has occurred."
}

.loading-progress {
    position: relative;
    display: block;
    width: 8rem;
    height: 8rem;
    margin: 20vh auto 1rem auto;
}

.loading-progress circle {
    fill: none;
    stroke: #e0e0e0;
    stroke-width: 0.6rem;
    transform-origin: 50% 50%;
    transform: rotate(-90deg);
}

.loading-progress circle:last-child {
    stroke: #1b6ec2;
    stroke-dasharray: calc(3.141 * var(--blazor-load-percentage, 0%) * 0.8), 500%;
    transition: stroke-dasharray 0.05s ease-in-out;
}

.loading-progress-text {
    position: absolute;
    text-align: center;
    font-weight: bold;
    inset: calc(20vh + 3.25rem) 0 auto 0.2rem;
}

.loading-progress-text:after {
    content: var(--blazor-load-percentage-text, "Loading");
}