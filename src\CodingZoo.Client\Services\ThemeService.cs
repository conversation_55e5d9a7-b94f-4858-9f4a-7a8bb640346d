using Microsoft.JSInterop;

namespace CodingZoo.Client.Services;

public class ThemeService : IThemeService
{
    private readonly IJSRuntime _jsRuntime;
    private bool _isDarkMode;
    private bool _initialized;

    public event Action<bool>? ThemeChanged;

    public ThemeService(IJSRuntime jsRuntime)
    {
        _jsRuntime = jsRuntime;
    }

    public async Task<bool> GetIsDarkModeAsync()
    {
        if (!_initialized)
        {
            await InitializeAsync();
        }
        return _isDarkMode;
    }

    public async Task SetDarkModeAsync(bool isDarkMode)
    {
        _isDarkMode = isDarkMode;
        await _jsRuntime.InvokeVoidAsync("localStorage.setItem", "darkMode", isDarkMode.ToString().ToLower());
        await ApplyThemeAsync();
        ThemeChanged?.Invoke(_isDarkMode);
    }

    public async Task ToggleDarkModeAsync()
    {
        await SetDarkModeAsync(!_isDarkMode);
    }

    private async Task InitializeAsync()
    {
        try
        {
            var storedTheme = await _jsRuntime.InvokeAsync<string?>("localStorage.getItem", "darkMode");
            
            if (storedTheme != null)
            {
                _isDarkMode = storedTheme.Equals("true", StringComparison.OrdinalIgnoreCase);
            }
            else
            {
                // Check system preference
                _isDarkMode = await _jsRuntime.InvokeAsync<bool>("window.matchMedia", "(prefers-color-scheme: dark)");
            }
            
            await ApplyThemeAsync();
            _initialized = true;
        }
        catch
        {
            // Fallback to light mode if there's an error
            _isDarkMode = false;
            _initialized = true;
        }
    }

    private async Task ApplyThemeAsync()
    {
        try
        {
            if (_isDarkMode)
            {
                await _jsRuntime.InvokeVoidAsync("document.documentElement.classList.add", "dark");
            }
            else
            {
                await _jsRuntime.InvokeVoidAsync("document.documentElement.classList.remove", "dark");
            }
        }
        catch
        {
            // Ignore JS interop errors during theme application
        }
    }
}
