{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=CodingZooDb;Trusted_Connection=true;MultipleActiveResultSets=true"}, "Jwt": {"SecretKey": "CodingZoo-Super-Secret-Key-For-JWT-Token-Generation-2024", "Issuer": "CodingZoo.API", "Audience": "CodingZoo.Client", "ExpirationMinutes": 60}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*"}