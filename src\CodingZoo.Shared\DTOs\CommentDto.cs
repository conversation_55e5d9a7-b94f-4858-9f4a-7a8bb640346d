using System.ComponentModel.DataAnnotations;

namespace CodingZoo.Shared.DTOs;

public class CommentDto
{
    public int Id { get; set; }
    public string Content { get; set; } = string.Empty;
    public bool IsApproved { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public int ArticleId { get; set; }
    public int UserId { get; set; }
    public int? ParentCommentId { get; set; }
    
    // Related data
    public UserDto User { get; set; } = null!;
    public List<CommentDto> Replies { get; set; } = new();
}

public class CreateCommentDto
{
    [Required]
    [StringLength(1000)]
    public string Content { get; set; } = string.Empty;
    
    [Required]
    public int ArticleId { get; set; }
    
    public int? ParentCommentId { get; set; }
}

public class UpdateCommentDto
{
    [Required]
    [StringLength(1000)]
    public string Content { get; set; } = string.Empty;
}

public class MediaDto
{
    public int Id { get; set; }
    public string FileName { get; set; } = string.Empty;
    public string OriginalFileName { get; set; } = string.Empty;
    public string ContentType { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string FilePath { get; set; } = string.Empty;
    public string AltText { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public int UploadedByUserId { get; set; }
    public UserDto UploadedBy { get; set; } = null!;
    public string FileUrl => $"/api/media/{FileName}";
    public string FileSizeFormatted => FormatFileSize(FileSize);
    
    private static string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }
}

public class UploadMediaDto
{
    [Required]
    public string FileName { get; set; } = string.Empty;
    
    [Required]
    public string ContentType { get; set; } = string.Empty;
    
    [Required]
    public byte[] FileData { get; set; } = Array.Empty<byte>();
    
    [StringLength(200)]
    public string AltText { get; set; } = string.Empty;
}
