using CodingZoo.Shared.Models;
using CodingZoo.Shared.DTOs;

namespace CodingZoo.API.Repositories;

public interface IArticleRepository : IRepository<Article>
{
    Task<(IEnumerable<Article> Articles, int TotalCount)> SearchArticlesAsync(ArticleSearchDto searchDto);
    Task<IEnumerable<Article>> GetFeaturedArticlesAsync(int count = 6);
    Task<IEnumerable<Article>> GetRecentArticlesAsync(int count = 10);
    Task<IEnumerable<Article>> GetPopularArticlesAsync(int count = 10);
    Task<IEnumerable<Article>> GetArticlesByCategoryAsync(int categoryId, int page = 1, int pageSize = 10);
    Task<Article?> GetArticleBySlugAsync(string slug);
    Task<IEnumerable<Article>> GetRelatedArticlesAsync(int articleId, int count = 5);
    Task IncrementViewCountAsync(int articleId);
    Task<bool> IsSlugUniqueAsync(string slug, int? excludeId = null);
}

public interface ICategoryRepository : IRepository<Category>
{
    Task<Category?> GetCategoryBySlugAsync(string slug);
    Task<IEnumerable<Category>> GetActiveCategoriesAsync();
    Task<bool> IsSlugUniqueAsync(string slug, int? excludeId = null);
    Task<Dictionary<int, int>> GetArticleCountsByCategoryAsync();
}

public interface IUserRepository : IRepository<User>
{
    Task<User?> GetUserByEmailAsync(string email);
    Task<User?> GetUserByUsernameAsync(string username);
    Task<bool> IsEmailUniqueAsync(string email, int? excludeId = null);
    Task<bool> IsUsernameUniqueAsync(string username, int? excludeId = null);
    Task<User?> AuthenticateAsync(string email, string password);
    Task UpdateLastLoginAsync(int userId);
}

public interface ICommentRepository : IRepository<Comment>
{
    Task<IEnumerable<Comment>> GetCommentsByArticleAsync(int articleId);
    Task<IEnumerable<Comment>> GetPendingCommentsAsync();
    Task ApproveCommentAsync(int commentId);
    Task RejectCommentAsync(int commentId);
}

public interface IMediaRepository : IRepository<Media>
{
    Task<IEnumerable<Media>> GetMediaByUserAsync(int userId);
    Task<Media?> GetMediaByFileNameAsync(string fileName);
}
