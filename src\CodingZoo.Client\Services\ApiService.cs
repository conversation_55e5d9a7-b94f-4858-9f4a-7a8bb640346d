using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using CodingZoo.Shared.Common;

namespace CodingZoo.Client.Services;

public class ApiService : IApiService
{
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;

    public ApiService(HttpClient httpClient)
    {
        _httpClient = httpClient;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };
    }

    public async Task<ApiResponse<T>?> GetAsync<T>(string endpoint)
    {
        try
        {
            var response = await _httpClient.GetAsync(endpoint);
            var content = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                return JsonSerializer.Deserialize<ApiResponse<T>>(content, _jsonOptions);
            }
            
            var errorResponse = JsonSerializer.Deserialize<ApiResponse<T>>(content, _jsonOptions);
            return errorResponse ?? ApiResponse<T>.ErrorResult("An error occurred while processing the request.");
        }
        catch (Exception ex)
        {
            return ApiResponse<T>.ErrorResult($"Network error: {ex.Message}");
        }
    }

    public async Task<ApiResponse<T>?> PostAsync<T>(string endpoint, object data)
    {
        try
        {
            var json = JsonSerializer.Serialize(data, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync(endpoint, content);
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                return JsonSerializer.Deserialize<ApiResponse<T>>(responseContent, _jsonOptions);
            }
            
            var errorResponse = JsonSerializer.Deserialize<ApiResponse<T>>(responseContent, _jsonOptions);
            return errorResponse ?? ApiResponse<T>.ErrorResult("An error occurred while processing the request.");
        }
        catch (Exception ex)
        {
            return ApiResponse<T>.ErrorResult($"Network error: {ex.Message}");
        }
    }

    public async Task<ApiResponse<T>?> PutAsync<T>(string endpoint, object data)
    {
        try
        {
            var json = JsonSerializer.Serialize(data, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PutAsync(endpoint, content);
            var responseContent = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                return JsonSerializer.Deserialize<ApiResponse<T>>(responseContent, _jsonOptions);
            }
            
            var errorResponse = JsonSerializer.Deserialize<ApiResponse<T>>(responseContent, _jsonOptions);
            return errorResponse ?? ApiResponse<T>.ErrorResult("An error occurred while processing the request.");
        }
        catch (Exception ex)
        {
            return ApiResponse<T>.ErrorResult($"Network error: {ex.Message}");
        }
    }

    public async Task<ApiResponse?> DeleteAsync(string endpoint)
    {
        try
        {
            var response = await _httpClient.DeleteAsync(endpoint);
            var content = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                return JsonSerializer.Deserialize<ApiResponse>(content, _jsonOptions);
            }
            
            var errorResponse = JsonSerializer.Deserialize<ApiResponse>(content, _jsonOptions);
            return errorResponse ?? ApiResponse.ErrorResult("An error occurred while processing the request.");
        }
        catch (Exception ex)
        {
            return ApiResponse.ErrorResult($"Network error: {ex.Message}");
        }
    }

    public void SetAuthToken(string token)
    {
        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
    }

    public void ClearAuthToken()
    {
        _httpClient.DefaultRequestHeaders.Authorization = null;
    }
}
