using System.ComponentModel.DataAnnotations;

namespace CodingZoo.Shared.DTOs;

public class ArticleDto
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Slug { get; set; } = string.Empty;
    public string Summary { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string FeaturedImage { get; set; } = string.Empty;
    public string MetaTitle { get; set; } = string.Empty;
    public string MetaDescription { get; set; } = string.Empty;
    public string Tags { get; set; } = string.Empty;
    public int ReadTimeMinutes { get; set; }
    public int ViewCount { get; set; }
    public bool IsFeatured { get; set; }
    public bool IsPublished { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public DateTime? PublishedAt { get; set; }
    public int CategoryId { get; set; }
    public int AuthorId { get; set; }
    
    // Related data
    public CategoryDto? Category { get; set; }
    public UserDto? Author { get; set; }
    public List<string> TagList => Tags.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList();
}

public class ArticleListDto
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Slug { get; set; } = string.Empty;
    public string Summary { get; set; } = string.Empty;
    public string FeaturedImage { get; set; } = string.Empty;
    public int ReadTimeMinutes { get; set; }
    public int ViewCount { get; set; }
    public bool IsFeatured { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? PublishedAt { get; set; }
    public CategoryDto Category { get; set; } = null!;
    public UserDto Author { get; set; } = null!;
    public List<string> TagList { get; set; } = new();
}

public class CreateArticleDto
{
    [Required]
    [StringLength(200)]
    public string Title { get; set; } = string.Empty;
    
    [StringLength(500)]
    public string Summary { get; set; } = string.Empty;
    
    [Required]
    public string Content { get; set; } = string.Empty;
    
    [StringLength(200)]
    public string FeaturedImage { get; set; } = string.Empty;
    
    [StringLength(200)]
    public string MetaTitle { get; set; } = string.Empty;
    
    [StringLength(300)]
    public string MetaDescription { get; set; } = string.Empty;
    
    [StringLength(500)]
    public string Tags { get; set; } = string.Empty;
    
    public int ReadTimeMinutes { get; set; }
    public bool IsFeatured { get; set; }
    public bool IsPublished { get; set; }
    
    [Required]
    public int CategoryId { get; set; }
}

public class UpdateArticleDto
{
    [Required]
    [StringLength(200)]
    public string Title { get; set; } = string.Empty;
    
    [StringLength(500)]
    public string Summary { get; set; } = string.Empty;
    
    [Required]
    public string Content { get; set; } = string.Empty;
    
    [StringLength(200)]
    public string FeaturedImage { get; set; } = string.Empty;
    
    [StringLength(200)]
    public string MetaTitle { get; set; } = string.Empty;
    
    [StringLength(300)]
    public string MetaDescription { get; set; } = string.Empty;
    
    [StringLength(500)]
    public string Tags { get; set; } = string.Empty;
    
    public int ReadTimeMinutes { get; set; }
    public bool IsFeatured { get; set; }
    public bool IsPublished { get; set; }
    
    [Required]
    public int CategoryId { get; set; }
}

public class ArticleSearchDto
{
    public string? Query { get; set; }
    public int? CategoryId { get; set; }
    public string? Tags { get; set; }
    public bool? IsFeatured { get; set; }
    public bool? IsPublished { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string SortBy { get; set; } = "CreatedAt";
    public string SortDirection { get; set; } = "desc";
}

public class PaginatedResult<T>
{
    public List<T> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasNextPage => Page < TotalPages;
    public bool HasPreviousPage => Page > 1;
}
