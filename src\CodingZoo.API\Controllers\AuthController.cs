using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AutoMapper;
using CodingZoo.API.Repositories;
using CodingZoo.API.Services;
using CodingZoo.Shared.Models;
using CodingZoo.Shared.DTOs;
using CodingZoo.Shared.Common;
using CodingZoo.Shared.Constants;

namespace CodingZoo.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    private readonly IUserRepository _userRepository;
    private readonly IJwtService _jwtService;
    private readonly IMapper _mapper;

    public AuthController(
        IUserRepository userRepository,
        IJwtService jwtService,
        IMapper mapper)
    {
        _userRepository = userRepository;
        _jwtService = jwtService;
        _mapper = mapper;
    }

    [HttpPost("login")]
    public async Task<ActionResult<ApiResponse<LoginResponseDto>>> Login([FromBody] LoginDto loginDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                return BadRequest(ApiResponse<LoginResponseDto>.ErrorResult(errors));
            }

            var user = await _userRepository.AuthenticateAsync(loginDto.Email, loginDto.Password);
            if (user == null)
            {
                return Unauthorized(ApiResponse<LoginResponseDto>.ErrorResult("Invalid email or password."));
            }

            if (!user.IsActive)
            {
                return Unauthorized(ApiResponse<LoginResponseDto>.ErrorResult("Account is deactivated."));
            }

            // Update last login
            await _userRepository.UpdateLastLoginAsync(user.Id);

            // Generate JWT token
            var token = _jwtService.GenerateToken(user);
            var userDto = _mapper.Map<UserDto>(user);

            var response = new LoginResponseDto
            {
                Token = token,
                User = userDto,
                ExpiresAt = DateTime.UtcNow.AddMinutes(60) // Should match JWT expiration
            };

            return Ok(ApiResponse<LoginResponseDto>.SuccessResult(response, "Login successful."));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<LoginResponseDto>.ErrorResult("An error occurred during login."));
        }
    }

    [HttpPost("register")]
    public async Task<ActionResult<ApiResponse<UserDto>>> Register([FromBody] CreateUserDto createUserDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                return BadRequest(ApiResponse<UserDto>.ErrorResult(errors));
            }

            // Check if email is already taken
            if (!await _userRepository.IsEmailUniqueAsync(createUserDto.Email))
            {
                return BadRequest(ApiResponse<UserDto>.ErrorResult("Email is already registered."));
            }

            // Check if username is already taken
            if (!await _userRepository.IsUsernameUniqueAsync(createUserDto.Username))
            {
                return BadRequest(ApiResponse<UserDto>.ErrorResult("Username is already taken."));
            }

            var user = _mapper.Map<User>(createUserDto);
            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(createUserDto.Password);
            user.CreatedAt = DateTime.UtcNow;
            user.UpdatedAt = DateTime.UtcNow;

            var createdUser = await _userRepository.AddAsync(user);
            var userDto = _mapper.Map<UserDto>(createdUser);

            return CreatedAtAction("GetProfile", "Users", new { }, 
                ApiResponse<UserDto>.SuccessResult(userDto, "Registration successful."));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<UserDto>.ErrorResult("An error occurred during registration."));
        }
    }

    [HttpGet("profile")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<UserDto>>> GetProfile()
    {
        try
        {
            var userIdClaim = User.FindFirst(AppConstants.Claims.UserId);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                return Unauthorized(ApiResponse<UserDto>.ErrorResult("Invalid user token."));
            }

            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                return NotFound(ApiResponse<UserDto>.ErrorResult("User not found."));
            }

            var userDto = _mapper.Map<UserDto>(user);
            return Ok(ApiResponse<UserDto>.SuccessResult(userDto));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<UserDto>.ErrorResult("An error occurred while retrieving profile."));
        }
    }

    [HttpPut("profile")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<UserDto>>> UpdateProfile([FromBody] UpdateUserDto updateUserDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                return BadRequest(ApiResponse<UserDto>.ErrorResult(errors));
            }

            var userIdClaim = User.FindFirst(AppConstants.Claims.UserId);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                return Unauthorized(ApiResponse<UserDto>.ErrorResult("Invalid user token."));
            }

            var existingUser = await _userRepository.GetByIdAsync(userId);
            if (existingUser == null)
            {
                return NotFound(ApiResponse<UserDto>.ErrorResult("User not found."));
            }

            // Check if email is unique (excluding current user)
            if (!await _userRepository.IsEmailUniqueAsync(updateUserDto.Email, userId))
            {
                return BadRequest(ApiResponse<UserDto>.ErrorResult("Email is already registered."));
            }

            // Check if username is unique (excluding current user)
            if (!await _userRepository.IsUsernameUniqueAsync(updateUserDto.Username, userId))
            {
                return BadRequest(ApiResponse<UserDto>.ErrorResult("Username is already taken."));
            }

            _mapper.Map(updateUserDto, existingUser);
            existingUser.UpdatedAt = DateTime.UtcNow;

            await _userRepository.UpdateAsync(existingUser);
            var userDto = _mapper.Map<UserDto>(existingUser);

            return Ok(ApiResponse<UserDto>.SuccessResult(userDto, "Profile updated successfully."));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<UserDto>.ErrorResult("An error occurred while updating profile."));
        }
    }

    [HttpPost("change-password")]
    [Authorize]
    public async Task<ActionResult<ApiResponse>> ChangePassword([FromBody] ChangePasswordDto changePasswordDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                return BadRequest(ApiResponse.ErrorResult(errors));
            }

            var userIdClaim = User.FindFirst(AppConstants.Claims.UserId);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                return Unauthorized(ApiResponse.ErrorResult("Invalid user token."));
            }

            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                return NotFound(ApiResponse.ErrorResult("User not found."));
            }

            // Verify current password
            if (!BCrypt.Net.BCrypt.Verify(changePasswordDto.CurrentPassword, user.PasswordHash))
            {
                return BadRequest(ApiResponse.ErrorResult("Current password is incorrect."));
            }

            // Update password
            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(changePasswordDto.NewPassword);
            user.UpdatedAt = DateTime.UtcNow;

            await _userRepository.UpdateAsync(user);

            return Ok(ApiResponse.SuccessResult("Password changed successfully."));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse.ErrorResult("An error occurred while changing password."));
        }
    }

    [HttpPost("validate-token")]
    public ActionResult<ApiResponse<bool>> ValidateToken([FromBody] string token)
    {
        try
        {
            var isValid = _jwtService.ValidateToken(token);
            return Ok(ApiResponse<bool>.SuccessResult(isValid));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<bool>.ErrorResult("An error occurred while validating token."));
        }
    }
}
