@page "/admin/login"
@using CodingZoo.Client.Services
@using CodingZoo.Shared.DTOs
@inject IAuthService AuthService
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<PageTitle>Admin Login - CodingZoo</PageTitle>

<div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="flex justify-center">
                <div class="w-16 h-16 bg-gradient-to-r from-primary-600 to-accent-600 rounded-xl flex items-center justify-center">
                    <span class="text-white font-bold text-2xl">CZ</span>
                </div>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
                Admin Panel Login
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
                Sign in to manage CodingZoo content
            </p>
        </div>

        <div class="card">
            <div class="card-body">
                @if (!string.IsNullOrEmpty(errorMessage))
                {
                    <div class="mb-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-red-800 dark:text-red-200">@errorMessage</p>
                            </div>
                        </div>
                    </div>
                }

                <EditForm Model="loginModel" OnValidSubmit="HandleLogin">
                    <DataAnnotationsValidator />
                    
                    <div class="space-y-6">
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Email Address
                            </label>
                            <InputText id="email" @bind-Value="loginModel.Email" 
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                                       placeholder="<EMAIL>" />
                            <ValidationMessage For="@(() => loginModel.Email)" class="text-red-600 text-sm mt-1" />
                        </div>

                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Password
                            </label>
                            <InputText id="password" type="password" @bind-Value="loginModel.Password" 
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                                       placeholder="Enter your password" />
                            <ValidationMessage For="@(() => loginModel.Password)" class="text-red-600 text-sm mt-1" />
                        </div>

                        <div class="flex items-center">
                            <InputCheckbox id="remember-me" @bind-Value="loginModel.RememberMe" 
                                           class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" />
                            <label for="remember-me" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                                Remember me
                            </label>
                        </div>

                        <div>
                            <button type="submit" disabled="@isLoading" 
                                    class="w-full btn-primary flex justify-center py-3 text-base font-medium">
                                @if (isLoading)
                                {
                                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Signing in...
                                }
                                else
                                {
                                    <span>Sign in to Admin Panel</span>
                                }
                            </button>
                        </div>
                    </div>
                </EditForm>

                <div class="mt-6 text-center">
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        <strong>Default Admin Credentials:</strong><br />
                        Email: <EMAIL><br />
                        Password: Admin123!
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center">
            <a href="/" class="text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300 text-sm font-medium">
                ← Back to CodingZoo
            </a>
        </div>
    </div>
</div>

@code {
    private LoginDto loginModel = new();
    private string errorMessage = string.Empty;
    private bool isLoading = false;

    protected override async Task OnInitializedAsync()
    {
        // Check if already authenticated and redirect to admin dashboard
        if (await AuthService.IsAuthenticatedAsync())
        {
            var user = await AuthService.GetCurrentUserAsync();
            if (user?.IsAdmin == true)
            {
                Navigation.NavigateTo("/admin");
                return;
            }
        }

        // Pre-fill with default admin credentials for demo
        loginModel.Email = "<EMAIL>";
        loginModel.Password = "Admin123!";
    }

    private async Task HandleLogin()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;

            var response = await AuthService.LoginAsync(loginModel);
            
            if (response?.Success == true && response.Data?.User?.IsAdmin == true)
            {
                await JSRuntime.InvokeVoidAsync("alert", "Login successful! Redirecting to admin dashboard...");
                Navigation.NavigateTo("/admin");
            }
            else if (response?.Data?.User?.IsAdmin == false)
            {
                errorMessage = "Access denied. Admin privileges required.";
            }
            else
            {
                errorMessage = response?.Message ?? "Invalid email or password.";
            }
        }
        catch (Exception ex)
        {
            errorMessage = "An error occurred during login. Please try again.";
            Console.WriteLine($"Login error: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }
}
