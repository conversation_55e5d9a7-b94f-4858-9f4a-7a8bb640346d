﻿using System.ComponentModel.DataAnnotations;

namespace CodingZoo.Shared.Models;

public class Category
{
    public int Id { get; set; }

    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    [StringLength(200)]
    public string Description { get; set; } = string.Empty;

    [StringLength(50)]
    public string Slug { get; set; } = string.Empty;

    [StringLength(100)]
    public string Icon { get; set; } = string.Empty;

    [StringLength(20)]
    public string Color { get; set; } = string.Empty;

    public int SortOrder { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual ICollection<Article> Articles { get; set; } = new List<Article>();
}

public class Article
{
    public int Id { get; set; }

    [Required]
    [StringLength(200)]
    public string Title { get; set; } = string.Empty;

    [StringLength(300)]
    public string Slug { get; set; } = string.Empty;

    [StringLength(500)]
    public string Summary { get; set; } = string.Empty;

    [Required]
    public string Content { get; set; } = string.Empty;

    [StringLength(200)]
    public string FeaturedImage { get; set; } = string.Empty;

    [StringLength(200)]
    public string MetaTitle { get; set; } = string.Empty;

    [StringLength(300)]
    public string MetaDescription { get; set; } = string.Empty;

    [StringLength(500)]
    public string Tags { get; set; } = string.Empty;

    public int ReadTimeMinutes { get; set; }
    public int ViewCount { get; set; }
    public bool IsFeatured { get; set; }
    public bool IsPublished { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? PublishedAt { get; set; }

    // Foreign keys
    public int CategoryId { get; set; }
    public int AuthorId { get; set; }

    // Navigation properties
    public virtual Category Category { get; set; } = null!;
    public virtual User Author { get; set; } = null!;
    public virtual ICollection<Comment> Comments { get; set; } = new List<Comment>();
    public virtual ICollection<ArticleBookmark> Bookmarks { get; set; } = new List<ArticleBookmark>();
}

public class User
{
    public int Id { get; set; }

    [Required]
    [StringLength(100)]
    public string FirstName { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string LastName { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;

    [StringLength(100)]
    public string Username { get; set; } = string.Empty;

    [Required]
    public string PasswordHash { get; set; } = string.Empty;

    [StringLength(200)]
    public string Avatar { get; set; } = string.Empty;

    [StringLength(500)]
    public string Bio { get; set; } = string.Empty;

    [StringLength(100)]
    public string Website { get; set; } = string.Empty;

    [StringLength(100)]
    public string TwitterHandle { get; set; } = string.Empty;

    [StringLength(100)]
    public string GitHubHandle { get; set; } = string.Empty;

    public bool IsAdmin { get; set; }
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? LastLoginAt { get; set; }

    // Navigation properties
    public virtual ICollection<Article> Articles { get; set; } = new List<Article>();
    public virtual ICollection<Comment> Comments { get; set; } = new List<Comment>();
    public virtual ICollection<ArticleBookmark> Bookmarks { get; set; } = new List<ArticleBookmark>();
}

public class Comment
{
    public int Id { get; set; }

    [Required]
    [StringLength(1000)]
    public string Content { get; set; } = string.Empty;

    public bool IsApproved { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Foreign keys
    public int ArticleId { get; set; }
    public int UserId { get; set; }
    public int? ParentCommentId { get; set; }

    // Navigation properties
    public virtual Article Article { get; set; } = null!;
    public virtual User User { get; set; } = null!;
    public virtual Comment? ParentComment { get; set; }
    public virtual ICollection<Comment> Replies { get; set; } = new List<Comment>();
}

public class ArticleBookmark
{
    public int Id { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Foreign keys
    public int ArticleId { get; set; }
    public int UserId { get; set; }

    // Navigation properties
    public virtual Article Article { get; set; } = null!;
    public virtual User User { get; set; } = null!;
}

public class Media
{
    public int Id { get; set; }

    [Required]
    [StringLength(200)]
    public string FileName { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    public string OriginalFileName { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string ContentType { get; set; } = string.Empty;

    public long FileSize { get; set; }

    [Required]
    [StringLength(500)]
    public string FilePath { get; set; } = string.Empty;

    [StringLength(200)]
    public string AltText { get; set; } = string.Empty;

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Foreign key
    public int UploadedByUserId { get; set; }

    // Navigation property
    public virtual User UploadedBy { get; set; } = null!;
}
