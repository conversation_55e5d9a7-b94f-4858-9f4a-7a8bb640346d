@page "/admin/articles"
@using CodingZoo.Client.Services
@using CodingZoo.Shared.DTOs
@inject IAuthService AuthService
@inject IArticleService ArticleService
@inject ICategoryService CategoryService
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<PageTitle>Manage Articles - Admin - CodingZoo</PageTitle>

@if (isAuthorized)
{
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Manage Articles</h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">Create, edit, and manage your articles</p>
                </div>
                <div class="flex space-x-4">
                    <a href="/admin/articles/create" class="btn-primary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        New Article
                    </a>
                    <a href="/admin" class="btn-secondary">
                        ← Back to Dashboard
                    </a>
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="card mb-6">
            <div class="card-body">
                <div class="flex flex-col md:flex-row gap-4">
                    <div class="flex-1">
                        <input type="text" @bind="searchQuery" @onkeyup="HandleSearch" 
                               placeholder="Search articles..." 
                               class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-800 dark:text-white" />
                    </div>
                    <select @bind="selectedCategoryId" @onchange="HandleCategoryFilter" 
                            class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-800 dark:text-white">
                        <option value="">All Categories</option>
                        @if (categories != null)
                        {
                            @foreach (var category in categories)
                            {
                                <option value="@category.Id">@category.Name</option>
                            }
                        }
                    </select>
                    <button @onclick="LoadArticlesAsync" class="btn-secondary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Refresh
                    </button>
                </div>
            </div>
        </div>

        <!-- Articles Table -->
        <div class="card">
            <div class="card-body">
                @if (isLoading)
                {
                    <div class="text-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
                        <p class="mt-4 text-gray-600 dark:text-gray-400">Loading articles...</p>
                    </div>
                }
                else if (articles?.Any() == true)
                {
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-800">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Title</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Category</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Author</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Created</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                                @foreach (var article in articles)
                                {
                                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                                        <td class="px-6 py-4">
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">@article.Title</div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">@article.Summary</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="badge-primary">@article.Category.Name</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-white">@article.Author.FullName</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            @if (article.IsPublished)
                                            {
                                                <span class="badge-success">Published</span>
                                            }
                                            else
                                            {
                                                <span class="badge-warning">Draft</span>
                                            }
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            @article.CreatedAt?.ToString("MMM dd, yyyy")
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <a href="/articles/@article.Slug" target="_blank" 
                                                   class="text-blue-600 hover:text-blue-500 dark:text-blue-400" title="View">
                                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                    </svg>
                                                </a>
                                                <a href="/admin/articles/edit/@article.Id" 
                                                   class="text-indigo-600 hover:text-indigo-500 dark:text-indigo-400" title="Edit">
                                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                    </svg>
                                                </a>
                                                <button @onclick="() => DeleteArticle(article.Id)" 
                                                        class="text-red-600 hover:text-red-500 dark:text-red-400" title="Delete">
                                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-6 flex justify-between items-center">
                        <div class="text-sm text-gray-700 dark:text-gray-300">
                            Showing @((currentPage - 1) * pageSize + 1) to @Math.Min(currentPage * pageSize, totalCount) of @totalCount results
                        </div>
                        <div class="flex space-x-2">
                            <button @onclick="() => ChangePage(currentPage - 1)" 
                                    disabled="@(currentPage <= 1)"
                                    class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                Previous
                            </button>
                            @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(totalPages, currentPage + 2); i++)
                            {
                                <button @onclick="() => ChangePage(i)" 
                                        class="px-3 py-2 text-sm font-medium @(i == currentPage ? "text-primary-600 bg-primary-50 border-primary-300" : "text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700") border dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                    @i
                                </button>
                            }
                            <button @onclick="() => ChangePage(currentPage + 1)" 
                                    disabled="@(currentPage >= totalPages)"
                                    class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                                Next
                            </button>
                        </div>
                    </div>
                }
                else
                {
                    <div class="text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No articles found</h3>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by creating your first article.</p>
                        <div class="mt-6">
                            <a href="/admin/articles/create" class="btn-primary">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Create Article
                            </a>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
}
else if (isLoading)
{
    <div class="min-h-screen flex items-center justify-center">
        <div class="text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p class="mt-4 text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
    </div>
}
else
{
    <div class="min-h-screen flex items-center justify-center">
        <div class="text-center">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Access Denied</h1>
            <p class="text-gray-600 dark:text-gray-400 mb-6">You need admin privileges to access this page.</p>
            <a href="/admin/login" class="btn-primary">Login as Admin</a>
        </div>
    </div>
}

@code {
    private bool isAuthorized = false;
    private bool isLoading = true;
    private List<ArticleListDto>? articles;
    private List<CategoryDto>? categories;
    
    private string searchQuery = string.Empty;
    private string selectedCategoryId = string.Empty;
    private int currentPage = 1;
    private int pageSize = 10;
    private int totalCount = 0;
    private int totalPages => (int)Math.Ceiling((double)totalCount / pageSize);

    protected override async Task OnInitializedAsync()
    {
        await CheckAuthorizationAsync();
        if (isAuthorized)
        {
            await LoadCategoriesAsync();
            await LoadArticlesAsync();
        }
        isLoading = false;
    }

    private async Task CheckAuthorizationAsync()
    {
        try
        {
            if (await AuthService.IsAuthenticatedAsync())
            {
                var currentUser = await AuthService.GetCurrentUserAsync();
                isAuthorized = currentUser?.IsAdmin == true;
            }
            
            if (!isAuthorized)
            {
                Navigation.NavigateTo("/admin/login");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Authorization check error: {ex.Message}");
            Navigation.NavigateTo("/admin/login");
        }
    }

    private async Task LoadCategoriesAsync()
    {
        try
        {
            var response = await CategoryService.GetCategoriesAsync();
            if (response?.Success == true)
            {
                categories = response.Data;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading categories: {ex.Message}");
        }
    }

    private async Task LoadArticlesAsync()
    {
        try
        {
            isLoading = true;
            
            var searchDto = new ArticleSearchDto
            {
                Page = currentPage,
                PageSize = pageSize,
                SearchTerm = string.IsNullOrWhiteSpace(searchQuery) ? null : searchQuery,
                CategoryId = string.IsNullOrWhiteSpace(selectedCategoryId) ? null : int.Parse(selectedCategoryId)
            };

            var response = await ArticleService.GetArticlesAsync(searchDto.Page, searchDto.PageSize);
            if (response?.Success == true && response.Data != null)
            {
                articles = response.Data.Items;
                totalCount = response.Data.TotalCount;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading articles: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task HandleSearch()
    {
        currentPage = 1;
        await LoadArticlesAsync();
    }

    private async Task HandleCategoryFilter()
    {
        currentPage = 1;
        await LoadArticlesAsync();
    }

    private async Task ChangePage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            await LoadArticlesAsync();
        }
    }

    private async Task DeleteArticle(int articleId)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this article? This action cannot be undone."))
        {
            try
            {
                // Note: You'll need to implement DeleteArticleAsync in ArticleService
                // var response = await ArticleService.DeleteArticleAsync(articleId);
                // if (response?.Success == true)
                // {
                //     await LoadArticlesAsync();
                //     await JSRuntime.InvokeVoidAsync("alert", "Article deleted successfully!");
                // }
                await JSRuntime.InvokeVoidAsync("alert", "Delete functionality will be implemented when API is connected.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting article: {ex.Message}");
                await JSRuntime.InvokeVoidAsync("alert", "Error deleting article. Please try again.");
            }
        }
    }
}
