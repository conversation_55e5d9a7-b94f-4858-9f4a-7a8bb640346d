@page "/about"

<PageTitle>About - CodingZoo</PageTitle>

<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="text-center mb-12">
        <h1 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            About CodingZoo
        </h1>
        <p class="text-xl text-gray-600 dark:text-gray-400">
            Your Ultimate Coding Education Platform
        </p>
    </div>

    <!-- Mission Section -->
    <section class="mb-12">
        <div class="card">
            <div class="card-body">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Our Mission</h2>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                    At CodingZoo, we believe that learning to code should be accessible, engaging, and comprehensive. 
                    Our mission is to provide high-quality programming tutorials and guides that help developers 
                    at all levels master new technologies and advance their careers.
                </p>
                <p class="text-gray-600 dark:text-gray-400">
                    Whether you're a complete beginner taking your first steps into programming or an experienced 
                    developer looking to learn new frameworks, CodingZoo has the resources you need to succeed.
                </p>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="mb-12">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center">What We Offer</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="card">
                <div class="card-body">
                    <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-book text-primary-600 dark:text-primary-400 text-xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Comprehensive Tutorials</h3>
                    <p class="text-gray-600 dark:text-gray-400">
                        Step-by-step guides covering everything from basic syntax to advanced concepts.
                    </p>
                </div>
            </div>
            
            <div class="card">
                <div class="card-body">
                    <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-code text-primary-600 dark:text-primary-400 text-xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Multiple Languages</h3>
                    <p class="text-gray-600 dark:text-gray-400">
                        Learn popular programming languages including C#, JavaScript, Python, and more.
                    </p>
                </div>
            </div>
            
            <div class="card">
                <div class="card-body">
                    <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-users text-primary-600 dark:text-primary-400 text-xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Community Driven</h3>
                    <p class="text-gray-600 dark:text-gray-400">
                        Join a community of learners and experienced developers sharing knowledge.
                    </p>
                </div>
            </div>
            
            <div class="card">
                <div class="card-body">
                    <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center mb-4">
                        <i class="fas fa-mobile-alt text-primary-600 dark:text-primary-400 text-xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Mobile Friendly</h3>
                    <p class="text-gray-600 dark:text-gray-400">
                        Learn on any device with our responsive design and mobile-optimized content.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="mb-12">
        <div class="card bg-gradient-to-r from-primary-600 to-accent-600 dark:from-primary-800 dark:to-accent-800">
            <div class="card-body text-center text-white">
                <h2 class="text-2xl font-bold mb-8">CodingZoo by the Numbers</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div>
                        <div class="text-3xl font-bold mb-2">500+</div>
                        <div class="text-primary-100">Tutorials</div>
                    </div>
                    <div>
                        <div class="text-3xl font-bold mb-2">50K+</div>
                        <div class="text-primary-100">Learners</div>
                    </div>
                    <div>
                        <div class="text-3xl font-bold mb-2">20+</div>
                        <div class="text-primary-100">Languages</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Team Section -->
    <section class="text-center">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Powered by ArslanDevs</h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
            CodingZoo is proudly developed and maintained by ArslanDevs, a team dedicated to 
            creating exceptional educational resources for the developer community.
        </p>
        <div class="flex justify-center">
            <a href="/contact" class="btn-primary">
                Get in Touch
            </a>
        </div>
    </section>
</div>
