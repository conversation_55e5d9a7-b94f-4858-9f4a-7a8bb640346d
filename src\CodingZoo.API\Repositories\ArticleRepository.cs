using Microsoft.EntityFrameworkCore;
using CodingZoo.API.Data;
using CodingZoo.Shared.Models;
using CodingZoo.Shared.DTOs;

namespace CodingZoo.API.Repositories;

public class ArticleRepository : Repository<Article>, IArticleRepository
{
    public ArticleRepository(CodingZooDbContext context) : base(context)
    {
    }

    public async Task<(IEnumerable<Article> Articles, int TotalCount)> SearchArticlesAsync(ArticleSearchDto searchDto)
    {
        var query = _dbSet.Include(a => a.Category)
                          .Include(a => a.Author)
                          .AsQueryable();

        // Apply filters
        if (!string.IsNullOrEmpty(searchDto.Query))
        {
            query = query.Where(a => a.Title.Contains(searchDto.Query) || 
                                   a.Summary.Contains(searchDto.Query) ||
                                   a.Content.Contains(searchDto.Query) ||
                                   a.Tags.Contains(searchDto.Query));
        }

        if (searchDto.CategoryId.HasValue)
        {
            query = query.Where(a => a.CategoryId == searchDto.CategoryId.Value);
        }

        if (!string.IsNullOrEmpty(searchDto.Tags))
        {
            query = query.Where(a => a.Tags.Contains(searchDto.Tags));
        }

        if (searchDto.IsFeatured.HasValue)
        {
            query = query.Where(a => a.IsFeatured == searchDto.IsFeatured.Value);
        }

        if (searchDto.IsPublished.HasValue)
        {
            query = query.Where(a => a.IsPublished == searchDto.IsPublished.Value);
        }
        else
        {
            // Default to published articles only
            query = query.Where(a => a.IsPublished);
        }

        // Get total count
        var totalCount = await query.CountAsync();

        // Apply sorting
        query = searchDto.SortBy.ToLower() switch
        {
            "title" => searchDto.SortDirection.ToLower() == "desc" 
                ? query.OrderByDescending(a => a.Title) 
                : query.OrderBy(a => a.Title),
            "viewcount" => searchDto.SortDirection.ToLower() == "desc" 
                ? query.OrderByDescending(a => a.ViewCount) 
                : query.OrderBy(a => a.ViewCount),
            "publishedat" => searchDto.SortDirection.ToLower() == "desc" 
                ? query.OrderByDescending(a => a.PublishedAt) 
                : query.OrderBy(a => a.PublishedAt),
            _ => searchDto.SortDirection.ToLower() == "desc" 
                ? query.OrderByDescending(a => a.CreatedAt) 
                : query.OrderBy(a => a.CreatedAt)
        };

        // Apply paging
        var articles = await query
            .Skip((searchDto.Page - 1) * searchDto.PageSize)
            .Take(searchDto.PageSize)
            .ToListAsync();

        return (articles, totalCount);
    }

    public async Task<IEnumerable<Article>> GetFeaturedArticlesAsync(int count = 6)
    {
        return await _dbSet.Include(a => a.Category)
                          .Include(a => a.Author)
                          .Where(a => a.IsFeatured && a.IsPublished)
                          .OrderByDescending(a => a.PublishedAt)
                          .Take(count)
                          .ToListAsync();
    }

    public async Task<IEnumerable<Article>> GetRecentArticlesAsync(int count = 10)
    {
        return await _dbSet.Include(a => a.Category)
                          .Include(a => a.Author)
                          .Where(a => a.IsPublished)
                          .OrderByDescending(a => a.PublishedAt)
                          .Take(count)
                          .ToListAsync();
    }

    public async Task<IEnumerable<Article>> GetPopularArticlesAsync(int count = 10)
    {
        return await _dbSet.Include(a => a.Category)
                          .Include(a => a.Author)
                          .Where(a => a.IsPublished)
                          .OrderByDescending(a => a.ViewCount)
                          .Take(count)
                          .ToListAsync();
    }

    public async Task<IEnumerable<Article>> GetArticlesByCategoryAsync(int categoryId, int page = 1, int pageSize = 10)
    {
        return await _dbSet.Include(a => a.Category)
                          .Include(a => a.Author)
                          .Where(a => a.CategoryId == categoryId && a.IsPublished)
                          .OrderByDescending(a => a.PublishedAt)
                          .Skip((page - 1) * pageSize)
                          .Take(pageSize)
                          .ToListAsync();
    }

    public async Task<Article?> GetArticleBySlugAsync(string slug)
    {
        return await _dbSet.Include(a => a.Category)
                          .Include(a => a.Author)
                          .FirstOrDefaultAsync(a => a.Slug == slug);
    }

    public async Task<IEnumerable<Article>> GetRelatedArticlesAsync(int articleId, int count = 5)
    {
        var article = await _dbSet.FindAsync(articleId);
        if (article == null) return new List<Article>();

        return await _dbSet.Include(a => a.Category)
                          .Include(a => a.Author)
                          .Where(a => a.Id != articleId && 
                                     a.CategoryId == article.CategoryId && 
                                     a.IsPublished)
                          .OrderByDescending(a => a.ViewCount)
                          .Take(count)
                          .ToListAsync();
    }

    public async Task IncrementViewCountAsync(int articleId)
    {
        var article = await _dbSet.FindAsync(articleId);
        if (article != null)
        {
            article.ViewCount++;
            await _context.SaveChangesAsync();
        }
    }

    public async Task<bool> IsSlugUniqueAsync(string slug, int? excludeId = null)
    {
        var query = _dbSet.Where(a => a.Slug == slug);
        if (excludeId.HasValue)
        {
            query = query.Where(a => a.Id != excludeId.Value);
        }
        return !await query.AnyAsync();
    }
}
