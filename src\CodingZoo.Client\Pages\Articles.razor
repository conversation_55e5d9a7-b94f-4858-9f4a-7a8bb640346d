@page "/articles"
@using CodingZoo.Client.Services
@using CodingZoo.Shared.DTOs
@inject IArticleService ArticleService
@inject ICategoryService CategoryService

<PageTitle>Articles - CodingZoo</PageTitle>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            All Articles
        </h1>
        <p class="text-lg text-gray-600 dark:text-gray-400">
            Explore our comprehensive collection of programming tutorials and guides.
        </p>
    </div>

    <!-- Search and Filters -->
    <div class="mb-8 flex flex-col md:flex-row gap-4">
        <div class="flex-1">
            <input type="text" placeholder="Search articles..." 
                   class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-800 dark:text-white" />
        </div>
        <select class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-800 dark:text-white">
            <option value="">All Categories</option>
            <option value="csharp">C#</option>
            <option value="javascript">JavaScript</option>
            <option value="python">Python</option>
        </select>
    </div>

    <!-- Articles Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        @if (articles != null && articles.Any())
        {
            @foreach (var article in articles)
            {
                <article class="card-hover group">
                    @if (!string.IsNullOrEmpty(article.FeaturedImage))
                    {
                        <div class="aspect-video bg-gray-200 dark:bg-gray-700 rounded-t-xl overflow-hidden">
                            <img src="@article.FeaturedImage" alt="@article.Title" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" />
                        </div>
                    }
                    <div class="card-body">
                        <div class="flex items-center space-x-2 mb-3">
                            <span class="badge-primary">@article.Category.Name</span>
                            <span class="text-sm text-gray-500 dark:text-gray-400">@article.ReadTimeMinutes min read</span>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                            @article.Title
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-4 line-clamp-3">
                            @article.Summary
                        </p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
                                <span class="text-sm text-gray-600 dark:text-gray-400">@article.Author.FullName</span>
                            </div>
                            <span class="text-sm text-gray-500 dark:text-gray-500">@article.PublishedAt?.ToString("MMM dd")</span>
                        </div>
                    </div>
                </article>
            }
        }
        else
        {
            @for (int i = 0; i < 9; i++)
            {
                <div class="card">
                    <div class="aspect-video skeleton mb-4"></div>
                    <div class="card-body">
                        <div class="skeleton-text w-1/3 mb-3"></div>
                        <div class="skeleton-title mb-3"></div>
                        <div class="skeleton-text mb-2"></div>
                        <div class="skeleton-text mb-4 w-2/3"></div>
                        <div class="flex justify-between">
                            <div class="skeleton-text w-1/4"></div>
                            <div class="skeleton-text w-1/6"></div>
                        </div>
                    </div>
                </div>
            }
        }
    </div>

    <!-- Pagination -->
    <div class="mt-12 flex justify-center">
        <nav class="flex space-x-2">
            <button class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-lg hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                Previous
            </button>
            <button class="px-3 py-2 text-sm font-medium text-primary-600 bg-primary-50 border border-primary-300 hover:bg-primary-100 hover:text-primary-700 dark:bg-primary-900 dark:border-primary-700 dark:text-primary-400">
                1
            </button>
            <button class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                2
            </button>
            <button class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                3
            </button>
            <button class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-lg hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                Next
            </button>
        </nav>
    </div>
</div>

@code {
    private List<ArticleListDto>? articles;
    private List<CategoryDto>? categories;
    private bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadArticlesAsync();
    }

    private async Task LoadArticlesAsync()
    {
        try
        {
            isLoading = true;
            
            var response = await ArticleService.GetArticlesAsync(1, 12);
            if (response?.Success == true)
            {
                articles = response.Data?.Items;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading articles: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }
}
