using Microsoft.AspNetCore.Components;
using CodingZoo.Shared.DTOs;

namespace CodingZoo.Client.Pages;

public partial class Home : ComponentBase
{
    private List<ArticleListDto>? featuredArticles;
    private List<CategoryDto>? categories;
    private bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadDataAsync();
    }

    private async Task LoadDataAsync()
    {
        try
        {
            isLoading = true;
            
            // Load featured articles
            var articlesResponse = await ArticleService.GetFeaturedArticlesAsync(6);
            if (articlesResponse?.Success == true)
            {
                featuredArticles = articlesResponse.Data;
            }

            // Load categories
            var categoriesResponse = await CategoryService.GetCategoriesAsync();
            if (categoriesResponse?.Success == true)
            {
                categories = categoriesResponse.Data;
            }
        }
        catch (Exception ex)
        {
            // Handle error - could show a toast notification
            Console.WriteLine($"Error loading home page data: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }
}
