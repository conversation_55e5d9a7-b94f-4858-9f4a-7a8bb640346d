using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AutoMapper;
using CodingZoo.API.Repositories;
using CodingZoo.Shared.Models;
using CodingZoo.Shared.DTOs;
using CodingZoo.Shared.Common;
using CodingZoo.Shared.Constants;

namespace CodingZoo.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class CategoriesController : ControllerBase
{
    private readonly ICategoryRepository _categoryRepository;
    private readonly IArticleRepository _articleRepository;
    private readonly IMapper _mapper;

    public CategoriesController(
        ICategoryRepository categoryRepository,
        IArticleRepository articleRepository,
        IMapper mapper)
    {
        _categoryRepository = categoryRepository;
        _articleRepository = articleRepository;
        _mapper = mapper;
    }

    [HttpGet]
    public async Task<ActionResult<ApiResponse<List<CategoryDto>>>> GetCategories()
    {
        try
        {
            var categories = await _categoryRepository.GetActiveCategoriesAsync();
            var articleCounts = await _categoryRepository.GetArticleCountsByCategoryAsync();
            
            var categoryDtos = _mapper.Map<List<CategoryDto>>(categories);
            
            // Add article counts
            foreach (var categoryDto in categoryDtos)
            {
                categoryDto.ArticleCount = articleCounts.GetValueOrDefault(categoryDto.Id, 0);
            }

            return Ok(ApiResponse<List<CategoryDto>>.SuccessResult(categoryDtos));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<List<CategoryDto>>.ErrorResult("An error occurred while retrieving categories."));
        }
    }

    [HttpGet("{id:int}")]
    public async Task<ActionResult<ApiResponse<CategoryDto>>> GetCategory(int id)
    {
        try
        {
            var category = await _categoryRepository.GetByIdAsync(id);
            if (category == null)
            {
                return NotFound(ApiResponse<CategoryDto>.ErrorResult("Category not found."));
            }

            var categoryDto = _mapper.Map<CategoryDto>(category);
            var articleCounts = await _categoryRepository.GetArticleCountsByCategoryAsync();
            categoryDto.ArticleCount = articleCounts.GetValueOrDefault(categoryDto.Id, 0);

            return Ok(ApiResponse<CategoryDto>.SuccessResult(categoryDto));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<CategoryDto>.ErrorResult("An error occurred while retrieving the category."));
        }
    }

    [HttpGet("slug/{slug}")]
    public async Task<ActionResult<ApiResponse<CategoryDto>>> GetCategoryBySlug(string slug)
    {
        try
        {
            var category = await _categoryRepository.GetCategoryBySlugAsync(slug);
            if (category == null)
            {
                return NotFound(ApiResponse<CategoryDto>.ErrorResult("Category not found."));
            }

            var categoryDto = _mapper.Map<CategoryDto>(category);
            var articleCounts = await _categoryRepository.GetArticleCountsByCategoryAsync();
            categoryDto.ArticleCount = articleCounts.GetValueOrDefault(categoryDto.Id, 0);

            return Ok(ApiResponse<CategoryDto>.SuccessResult(categoryDto));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<CategoryDto>.ErrorResult("An error occurred while retrieving the category."));
        }
    }

    [HttpGet("{id:int}/articles")]
    public async Task<ActionResult<ApiResponse<List<ArticleListDto>>>> GetCategoryArticles(int id, [FromQuery] int page = 1, [FromQuery] int pageSize = 10)
    {
        try
        {
            var category = await _categoryRepository.GetByIdAsync(id);
            if (category == null)
            {
                return NotFound(ApiResponse<List<ArticleListDto>>.ErrorResult("Category not found."));
            }

            var articles = await _articleRepository.GetArticlesByCategoryAsync(id, page, pageSize);
            var articleDtos = _mapper.Map<List<ArticleListDto>>(articles);

            return Ok(ApiResponse<List<ArticleListDto>>.SuccessResult(articleDtos));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<List<ArticleListDto>>.ErrorResult("An error occurred while retrieving category articles."));
        }
    }

    [HttpPost]
    [Authorize(Roles = AppConstants.Roles.Admin)]
    public async Task<ActionResult<ApiResponse<CategoryDto>>> CreateCategory([FromBody] CreateCategoryDto createCategoryDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                return BadRequest(ApiResponse<CategoryDto>.ErrorResult(errors));
            }

            var category = _mapper.Map<Category>(createCategoryDto);
            category.Slug = GenerateSlug(createCategoryDto.Name);
            category.CreatedAt = DateTime.UtcNow;
            category.UpdatedAt = DateTime.UtcNow;

            // Ensure slug is unique
            var originalSlug = category.Slug;
            var counter = 1;
            while (!await _categoryRepository.IsSlugUniqueAsync(category.Slug))
            {
                category.Slug = $"{originalSlug}-{counter}";
                counter++;
            }

            var createdCategory = await _categoryRepository.AddAsync(category);
            var categoryDto = _mapper.Map<CategoryDto>(createdCategory);

            return CreatedAtAction(nameof(GetCategory), new { id = createdCategory.Id }, 
                ApiResponse<CategoryDto>.SuccessResult(categoryDto, "Category created successfully."));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<CategoryDto>.ErrorResult("An error occurred while creating the category."));
        }
    }

    [HttpPut("{id:int}")]
    [Authorize(Roles = AppConstants.Roles.Admin)]
    public async Task<ActionResult<ApiResponse<CategoryDto>>> UpdateCategory(int id, [FromBody] UpdateCategoryDto updateCategoryDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                return BadRequest(ApiResponse<CategoryDto>.ErrorResult(errors));
            }

            var existingCategory = await _categoryRepository.GetByIdAsync(id);
            if (existingCategory == null)
            {
                return NotFound(ApiResponse<CategoryDto>.ErrorResult("Category not found."));
            }

            _mapper.Map(updateCategoryDto, existingCategory);
            existingCategory.UpdatedAt = DateTime.UtcNow;

            // Update slug if name changed
            var newSlug = GenerateSlug(updateCategoryDto.Name);
            if (existingCategory.Slug != newSlug)
            {
                // Ensure new slug is unique
                var originalSlug = newSlug;
                var counter = 1;
                while (!await _categoryRepository.IsSlugUniqueAsync(newSlug, id))
                {
                    newSlug = $"{originalSlug}-{counter}";
                    counter++;
                }
                existingCategory.Slug = newSlug;
            }

            await _categoryRepository.UpdateAsync(existingCategory);
            var categoryDto = _mapper.Map<CategoryDto>(existingCategory);

            return Ok(ApiResponse<CategoryDto>.SuccessResult(categoryDto, "Category updated successfully."));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<CategoryDto>.ErrorResult("An error occurred while updating the category."));
        }
    }

    [HttpDelete("{id:int}")]
    [Authorize(Roles = AppConstants.Roles.Admin)]
    public async Task<ActionResult<ApiResponse>> DeleteCategory(int id)
    {
        try
        {
            var category = await _categoryRepository.GetByIdAsync(id);
            if (category == null)
            {
                return NotFound(ApiResponse.ErrorResult("Category not found."));
            }

            // Check if category has articles
            var articleCount = await _articleRepository.CountAsync(a => a.CategoryId == id);
            if (articleCount > 0)
            {
                return BadRequest(ApiResponse.ErrorResult("Cannot delete category that contains articles."));
            }

            await _categoryRepository.DeleteAsync(category);
            return Ok(ApiResponse.SuccessResult("Category deleted successfully."));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse.ErrorResult("An error occurred while deleting the category."));
        }
    }

    private static string GenerateSlug(string name)
    {
        return name.ToLowerInvariant()
            .Replace(" ", "-")
            .Replace(".", "")
            .Replace(",", "")
            .Replace("!", "")
            .Replace("?", "")
            .Replace(":", "")
            .Replace(";", "")
            .Replace("'", "")
            .Replace("\"", "")
            .Replace("(", "")
            .Replace(")", "")
            .Replace("[", "")
            .Replace("]", "")
            .Replace("{", "")
            .Replace("}", "")
            .Replace("/", "-")
            .Replace("\\", "-")
            .Replace("&", "and")
            .Replace("#", "sharp");
    }
}
