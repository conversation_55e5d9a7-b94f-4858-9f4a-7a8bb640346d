﻿<!-- Mobile Navigation Header -->
<div class="lg:hidden bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
    <div class="flex items-center justify-between px-4 py-3">
        <a href="/" class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-gradient-to-br from-primary-600 to-accent-600 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">CZ</span>
            </div>
            <span class="text-xl font-bold text-gray-900 dark:text-white">CodingZoo</span>
        </a>
        <button @onclick="ToggleNavMenu" class="p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        </button>
    </div>
</div>

<!-- Mobile Navigation Menu -->
<div class="@NavMenuCssClass lg:hidden bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
    <nav class="px-4 py-2 space-y-1">
        <NavLink href="/" Match="NavLinkMatch.All"
                 class="block px-3 py-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 font-medium">
            Home
        </NavLink>
        <NavLink href="/articles"
                 class="block px-3 py-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 font-medium">
            Articles
        </NavLink>
        <NavLink href="/categories"
                 class="block px-3 py-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 font-medium">
            Categories
        </NavLink>
        <NavLink href="/about"
                 class="block px-3 py-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 font-medium">
            About
        </NavLink>
        <NavLink href="/contact"
                 class="block px-3 py-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 font-medium">
            Contact
        </NavLink>
        <div class="pt-2 border-t border-gray-200 dark:border-gray-700">
            <button class="block w-full text-left px-3 py-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 font-medium">
                Sign In
            </button>
        </div>
    </nav>
</div>

@code {
    private bool collapseNavMenu = true;

    private string? NavMenuCssClass => collapseNavMenu ? "collapse" : null;

    private void ToggleNavMenu()
    {
        collapseNavMenu = !collapseNavMenu;
    }
}
