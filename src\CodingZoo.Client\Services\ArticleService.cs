using CodingZoo.Shared.DTOs;
using CodingZoo.Shared.Common;

namespace CodingZoo.Client.Services;

public class ArticleService : IArticleService
{
    private readonly IApiService _apiService;

    public ArticleService(IApiService apiService)
    {
        _apiService = apiService;
    }

    public async Task<ApiResponse<PaginatedResult<ArticleListDto>>?> GetArticlesAsync(ArticleSearchDto searchDto)
    {
        var queryParams = new List<string>();
        
        if (!string.IsNullOrEmpty(searchDto.Query))
            queryParams.Add($"query={Uri.EscapeDataString(searchDto.Query)}");
        
        if (searchDto.CategoryId.HasValue)
            queryParams.Add($"categoryId={searchDto.CategoryId}");
        
        if (!string.IsNullOrEmpty(searchDto.Tags))
            queryParams.Add($"tags={Uri.EscapeDataString(searchDto.Tags)}");
        
        if (searchDto.IsFeatured.HasValue)
            queryParams.Add($"isFeatured={searchDto.IsFeatured}");
        
        if (searchDto.IsPublished.HasValue)
            queryParams.Add($"isPublished={searchDto.IsPublished}");
        
        queryParams.Add($"page={searchDto.Page}");
        queryParams.Add($"pageSize={searchDto.PageSize}");
        queryParams.Add($"sortBy={searchDto.SortBy}");
        queryParams.Add($"sortDirection={searchDto.SortDirection}");
        
        var queryString = string.Join("&", queryParams);
        return await _apiService.GetAsync<PaginatedResult<ArticleListDto>>($"api/articles?{queryString}");
    }

    public async Task<ApiResponse<ArticleDto>?> GetArticleAsync(int id)
    {
        return await _apiService.GetAsync<ArticleDto>($"api/articles/{id}");
    }

    public async Task<ApiResponse<ArticleDto>?> GetArticleBySlugAsync(string slug)
    {
        return await _apiService.GetAsync<ArticleDto>($"api/articles/slug/{slug}");
    }

    public async Task<ApiResponse<List<ArticleListDto>>?> GetFeaturedArticlesAsync(int count = 6)
    {
        return await _apiService.GetAsync<List<ArticleListDto>>($"api/articles/featured?count={count}");
    }

    public async Task<ApiResponse<List<ArticleListDto>>?> GetRecentArticlesAsync(int count = 10)
    {
        return await _apiService.GetAsync<List<ArticleListDto>>($"api/articles/recent?count={count}");
    }

    public async Task<ApiResponse<List<ArticleListDto>>?> GetPopularArticlesAsync(int count = 10)
    {
        return await _apiService.GetAsync<List<ArticleListDto>>($"api/articles/popular?count={count}");
    }

    public async Task<ApiResponse<List<ArticleListDto>>?> GetRelatedArticlesAsync(int articleId, int count = 5)
    {
        return await _apiService.GetAsync<List<ArticleListDto>>($"api/articles/{articleId}/related?count={count}");
    }

    public async Task<ApiResponse<ArticleDto>?> CreateArticleAsync(CreateArticleDto createArticleDto)
    {
        return await _apiService.PostAsync<ArticleDto>("api/articles", createArticleDto);
    }

    public async Task<ApiResponse<ArticleDto>?> UpdateArticleAsync(int id, UpdateArticleDto updateArticleDto)
    {
        return await _apiService.PutAsync<ArticleDto>($"api/articles/{id}", updateArticleDto);
    }

    public async Task<ApiResponse?> DeleteArticleAsync(int id)
    {
        return await _apiService.DeleteAsync($"api/articles/{id}");
    }
}
