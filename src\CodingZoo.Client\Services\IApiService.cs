using CodingZoo.Shared.DTOs;
using CodingZoo.Shared.Common;

namespace CodingZoo.Client.Services;

public interface IApiService
{
    Task<ApiResponse<T>?> GetAsync<T>(string endpoint);
    Task<ApiResponse<T>?> PostAsync<T>(string endpoint, object data);
    Task<ApiResponse<T>?> PutAsync<T>(string endpoint, object data);
    Task<ApiResponse?> DeleteAsync(string endpoint);
    void SetAuthToken(string token);
    void ClearAuthToken();
}

public interface IArticleService
{
    Task<ApiResponse<PaginatedResult<ArticleListDto>>?> GetArticlesAsync(ArticleSearchDto searchDto);
    Task<ApiResponse<ArticleDto>?> GetArticleAsync(int id);
    Task<ApiResponse<ArticleDto>?> GetArticleBySlugAsync(string slug);
    Task<ApiResponse<List<ArticleListDto>>?> GetFeaturedArticlesAsync(int count = 6);
    Task<ApiResponse<List<ArticleListDto>>?> GetRecentArticlesAsync(int count = 10);
    Task<ApiResponse<List<ArticleListDto>>?> GetPopularArticlesAsync(int count = 10);
    Task<ApiResponse<List<ArticleListDto>>?> GetRelatedArticlesAsync(int articleId, int count = 5);
    Task<ApiResponse<ArticleDto>?> CreateArticleAsync(CreateArticleDto createArticleDto);
    Task<ApiResponse<ArticleDto>?> UpdateArticleAsync(int id, UpdateArticleDto updateArticleDto);
    Task<ApiResponse?> DeleteArticleAsync(int id);
}

public interface ICategoryService
{
    Task<ApiResponse<List<CategoryDto>>?> GetCategoriesAsync();
    Task<ApiResponse<CategoryDto>?> GetCategoryAsync(int id);
    Task<ApiResponse<CategoryDto>?> GetCategoryBySlugAsync(string slug);
    Task<ApiResponse<List<ArticleListDto>>?> GetCategoryArticlesAsync(int categoryId, int page = 1, int pageSize = 10);
    Task<ApiResponse<CategoryDto>?> CreateCategoryAsync(CreateCategoryDto createCategoryDto);
    Task<ApiResponse<CategoryDto>?> UpdateCategoryAsync(int id, UpdateCategoryDto updateCategoryDto);
    Task<ApiResponse?> DeleteCategoryAsync(int id);
}

public interface IAuthService
{
    Task<ApiResponse<LoginResponseDto>?> LoginAsync(LoginDto loginDto);
    Task<ApiResponse<UserDto>?> RegisterAsync(CreateUserDto createUserDto);
    Task<ApiResponse<UserDto>?> GetProfileAsync();
    Task<ApiResponse<UserDto>?> UpdateProfileAsync(UpdateUserDto updateUserDto);
    Task<ApiResponse?> ChangePasswordAsync(ChangePasswordDto changePasswordDto);
    Task<ApiResponse<bool>?> ValidateTokenAsync(string token);
    Task LogoutAsync();
    Task<bool> IsAuthenticatedAsync();
    Task<string?> GetTokenAsync();
    Task<UserDto?> GetCurrentUserAsync();
}
