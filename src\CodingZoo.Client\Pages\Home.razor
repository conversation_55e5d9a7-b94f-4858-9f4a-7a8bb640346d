﻿@page "/"
@using CodingZoo.Client.Services
@using CodingZoo.Shared.DTOs
@using CodingZoo.Shared.Constants
@inject IArticleService ArticleService
@inject ICategoryService CategoryService

<PageTitle>CodingZoo - Your Ultimate Coding Education Platform</PageTitle>

<!-- Hero Section -->
<section class="relative overflow-hidden bg-gradient-to-br from-primary-600 via-primary-700 to-accent-600 dark:from-primary-800 dark:via-primary-900 dark:to-accent-800">
    <div class="absolute inset-0 bg-pattern opacity-10"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
        <div class="text-center">
            <h1 class="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 animate-fade-in-up">
                Welcome to <span class="text-accent-300">CodingZoo</span>
            </h1>
            <p class="text-xl md:text-2xl text-primary-100 mb-8 max-w-3xl mx-auto animate-fade-in-up" style="animation-delay: 0.2s;">
                Your Ultimate Coding Education Platform. Master programming languages, frameworks, and best practices with our comprehensive tutorials and guides.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in-up" style="animation-delay: 0.4s;">
                <button class="btn-lg bg-white text-primary-600 hover:bg-gray-100 font-semibold">
                    Start Learning
                </button>
                <button class="btn-lg btn-outline border-white text-white hover:bg-white hover:text-primary-600">
                    Browse Articles
                </button>
            </div>
        </div>
    </div>

    <!-- Floating Elements -->
    <div class="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full animate-float"></div>
    <div class="absolute top-40 right-20 w-16 h-16 bg-accent-400/20 rounded-full animate-float" style="animation-delay: 1s;"></div>
    <div class="absolute bottom-20 left-1/4 w-12 h-12 bg-white/10 rounded-full animate-float" style="animation-delay: 2s;"></div>
</section>

<!-- Popular Languages Section -->
<section class="py-16 lg:py-24 bg-white dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Popular Programming Languages
            </h2>
            <p class="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                Explore our comprehensive tutorials and guides for the most in-demand programming languages and technologies.
            </p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            @foreach (var language in AppConstants.PopularLanguages.Languages)
            {
                <div class="card-hover text-center p-6 group cursor-pointer">
                    <div class="flex justify-center mb-4">
                        <i class="@language.Icon text-4xl group-hover:scale-110 transition-transform duration-200" style="color: @language.Color;"></i>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">@language.Name</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Learn @language.Name</p>
                </div>
            }
        </div>
    </div>
</section>

<!-- Featured Articles Section -->
<section class="py-16 lg:py-24 bg-gray-50 dark:bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center mb-12">
            <div>
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                    Featured Articles
                </h2>
                <p class="text-lg text-gray-600 dark:text-gray-400">
                    Discover our most popular and comprehensive programming tutorials.
                </p>
            </div>
            <a href="/articles" class="btn-primary hidden md:inline-flex">
                View All Articles
            </a>
        </div>

        @if (featuredArticles != null)
        {
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach (var article in featuredArticles)
                {
                    <article class="card-hover group">
                        @if (!string.IsNullOrEmpty(article.FeaturedImage))
                        {
                            <div class="aspect-video bg-gray-200 dark:bg-gray-700 rounded-t-xl overflow-hidden">
                                <img src="@article.FeaturedImage" alt="@article.Title" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" />
                            </div>
                        }
                        <div class="card-body">
                            <div class="flex items-center space-x-2 mb-3">
                                <span class="badge-primary">@article.Category.Name</span>
                                <span class="text-sm text-gray-500 dark:text-gray-400">@article.ReadTimeMinutes min read</span>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                                @article.Title
                            </h3>
                            <p class="text-gray-600 dark:text-gray-400 mb-4 line-clamp-3">
                                @article.Summary
                            </p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <div class="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
                                    <span class="text-sm text-gray-600 dark:text-gray-400">@article.Author.FullName</span>
                                </div>
                                <span class="text-sm text-gray-500 dark:text-gray-500">@article.PublishedAt?.ToString("MMM dd")</span>
                            </div>
                        </div>
                    </article>
                }
            </div>
        }
        else
        {
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @for (int i = 0; i < 6; i++)
                {
                    <div class="card">
                        <div class="aspect-video skeleton mb-4"></div>
                        <div class="card-body">
                            <div class="skeleton-text w-1/3 mb-3"></div>
                            <div class="skeleton-title mb-3"></div>
                            <div class="skeleton-text mb-2"></div>
                            <div class="skeleton-text mb-4 w-2/3"></div>
                            <div class="flex justify-between">
                                <div class="skeleton-text w-1/4"></div>
                                <div class="skeleton-text w-1/6"></div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        }

        <div class="text-center mt-12 md:hidden">
            <a href="/articles" class="btn-primary">
                View All Articles
            </a>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="py-16 lg:py-24 bg-white dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div class="animate-fade-in-up">
                <div class="text-4xl md:text-5xl font-bold text-primary-600 dark:text-primary-400 mb-2">500+</div>
                <div class="text-lg text-gray-600 dark:text-gray-400">Comprehensive Tutorials</div>
            </div>
            <div class="animate-fade-in-up" style="animation-delay: 0.2s;">
                <div class="text-4xl md:text-5xl font-bold text-primary-600 dark:text-primary-400 mb-2">50K+</div>
                <div class="text-lg text-gray-600 dark:text-gray-400">Happy Learners</div>
            </div>
            <div class="animate-fade-in-up" style="animation-delay: 0.4s;">
                <div class="text-4xl md:text-5xl font-bold text-primary-600 dark:text-primary-400 mb-2">20+</div>
                <div class="text-lg text-gray-600 dark:text-gray-400">Programming Languages</div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-16 lg:py-24 bg-gradient-to-r from-primary-600 to-accent-600 dark:from-primary-800 dark:to-accent-800">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Start Your Coding Journey?
        </h2>
        <p class="text-xl text-primary-100 mb-8">
            Join thousands of developers who are already learning with CodingZoo. Start with our beginner-friendly tutorials or dive into advanced topics.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <button class="btn-lg bg-white text-primary-600 hover:bg-gray-100 font-semibold">
                Get Started Free
            </button>
            <button class="btn-lg btn-outline border-white text-white hover:bg-white hover:text-primary-600">
                Explore Categories
            </button>
        </div>
        <p class="text-sm text-primary-200 mt-6">
            Powered by <span class="font-semibold">ArslanDevs</span> • No credit card required
        </p>
    </div>
</section>
