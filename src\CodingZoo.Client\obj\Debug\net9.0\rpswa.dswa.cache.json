{"GlobalPropertiesHash": "bZPl0iaIL0t727Maz36yd9QlQSreXPdnVVIhG8E9P6o=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["cSUR2IbEiKk9Unhf6jOFpyvTAyGStnN0zAuoQSiLXTU=", "hcyGg9h/RPLjZE+NEj7faG0zeRAh+2Snke/1bvQgv7Q=", "VkC6exnX7zpFr5sMTajLSIvbhBUjWTesTczdXy6U8PM=", "6l41Mx5jgHkkyy6sPOY8BCr0iNMIjhdeqWCzfGGjSi8=", "mOXr0prBrFIAaj/+GKrBtYaMqsUSoKjJSnEXwUYhgPE=", "GdGFZgNBFO/PkYqXDPeEDjjx6ZM4jLaQVUSYnEd2LgE=", "PowIZePJC8J5hPSURwSYaOpxLGpJ8LjS4HyuyLbdQ0Y=", "0XAKJ6nKGds+k9z5e66GV+3NT7E8NvMI2W8+tUh/BJU=", "cQ2rY4EAdTrMdJDthcAfBEkcYMvEFbb4VUsOyAhB4yQ=", "bPrbp/+QS9+IrqhGks0GUlE6bsX0wb3RTC54OZkLWos=", "n0xz17dvuqkHCLuurDulJ9L0C8Qq12msAR8IAigg6HE=", "hE7vvj6H39uO9LNfB6jDiuTprW1EgVC7CRqH2ihoJsQ=", "mTPthGEBb8vOSmK8bYrb72RhXYiq2ILKAvdzlwHN5+o=", "EE5tEY0qjGadtFh8bmJniJlooyghhMY+jSTKwrIinn8=", "v07F1s8B6GeXboeaoiRJjJ6ohSr+7pZrxF2lv2hfsdA=", "hvlkYlwcAVMYXDqliNWJPszYWYB3tPgoYGBAoXRZIP8=", "C/wyA0FANQ5A7nOhfHjeZXk+UtIodMFt26eOwTuoqeM=", "Fo6GiVB9PwjxaZXYq8xSIQT7QPVfcTLZPZL2NSLIyCc=", "2w0GD/4EC7LHaKZRH55MecdvA7AVBPhMTkeZQlTiaNk=", "EEIDhNwWLumIskieuQXM1komKFq0mfz67b2KquM0sQ0=", "KX1H6pd+dEVeJtliNy1BrW+8m7HY6+YuYTYvYMiyUbE=", "E7UZ6m5fQ0QoQk5aDKZ1/GoxHdkTFROcOIqsX7RJ+g4=", "qFpL2p7W4fjQ5M0rzUBEmvfPwOzoq8k00mG/5nvVbho=", "jD9EtSUYwv/IjPblv7do1WAcUeYdpEN7lvwOhPmBrLE=", "Gckst/NVoBsTPJ40el2Ty4GFGg+T836D2HKNDfaQOTY=", "BN92IxFHVsQjaM3XKCc6dp7w6RfrxW02U3t+Kg3422I=", "OJVbFihkPnOyqBN4UXMMevP6RXFkG3uaxOwerBWB26I=", "pAzWQlV8WorgDQfU8Ju+6e+1gTYtG+Qyt64SICun3PM=", "G1lYdZHlrpKMOUnVRl0hjv/p+51UXZMhUjKZqcjj/54=", "ytRZKxCvWGbvni2EPD4losXiJkNPxCBdRF5f1m/em6c=", "EF0mLuMA0D4KIrRkZLZXPZ8Z9i39J2J5n4AE+wI7LaU=", "UqLgX+5Cu/MoT6PTiNIAJaBmZFlTZo/iVMdzfkJdvrI=", "38ZrDls1abKBpvOsTR4BCnKTTHRrTeX0scndqJgNbys=", "M0dS3ywqo4Jy1S6tvrI1ogzqZ0UcFFtpsM7/urZIUB8=", "gKDlhuCjbLNeQXE7i8gf2X54azaVxDHcL4h+/e7OQu8=", "URp6PeAFPepMErJbhMbSmqnhaKoOVPZIxM2+s0g/YlE=", "JLWnciNAQnuBverxarJLr5Pf2UvZ9Rq1W5lB4VK6rhQ=", "uPLOsYuM5Uf6BFMeKEj53jlnFGmFgndcHatpW2gnEoo=", "7TK+cR9nl8NUi1G0JI8GLRVvuqE+rxAfyLzUKs0Xbqk=", "FIMIkqHcMisY5mVJS+OUq9cWq63mdL/f0hkqWg6Eubo=", "JQJiI+DNT1c4L81xx2deWzVEP6PjIvLbFqeWVRcuH0I=", "4gSOKoQmbqWQ7wPSIJFglLCuSypuFWAwriF0yiL0ehA=", "bunvECXmCxX7i5c15CcIg4zQkx71Uye7XMKul1KoRK0=", "D4mUKefndNx/H14oNgFI/aUFHsVAyclopmDxcTQONRI=", "VZGgT/pIaUMfzVlakAIr6mI1HkjkAgks20mXxHBn2k0=", "ruvIgDkeT5Ciasx/PSNYCnAvuLzs9xbQ9KZpWSR+CG8=", "ZQw4A20juVeUm6tf7XRpoxucQ+lIZShIzma/1NytemA=", "lxT8GwR6QKhxLHRlYYldz9panRHDXcwqrfgRxN6FEKQ=", "oe313YlW8C3ctbgvdAAD9PODZjyZL3VQoB8ff/35A5w=", "exuXD6JeDNLxJAGlTSEFd5ml5IGVZYtqbDLB0/2z9GE=", "SyQmdvYk6oyupoKEDPizALefPSyQXVQtaAuUJ4qO/Io=", "nMJX9uLPEiaQQqzz6yko+NKvAnJmmIW9gwCGlvfUKCI=", "GFQ0WgqOaiprOwwSoanvrhN/GN8WrEK4sFXUEUJalPI=", "lnFkDNRA5+1+BY93v7nZNNTJhwFDJR1EicQnzM/YkMM=", "5xQIs9SP0fkEw9yn3wH211e6JkYWTL2RQfens0K9IdI=", "dKzpucNxCnLKfk9C2blFlWQB5zu+j2cELFFIyyCiKRQ=", "+CuUJOkMFLTdVEkXfVdVCQemegYc3KVzjvHssIs4Crs=", "jFlEFcDm9tkKZ4AR6mAtGxX1UR1l3uY/olTwFe5LcgA=", "Pm1Veai+GQyiPLzof0SWXSjsIFLGshMh0Wt2WslzItI=", "b6jos/6k8Rp1dVG04XxrsCj0zxQyxgrRCivmlxtifWg=", "CSsvS78smM0FXFynzIdbSD4fcN+igYO2GMCzFy+Wbvc=", "LGEX4O8GgyIfOMWKRtcvs7V4e+ieo7OWEq4gOJ9NrBo=", "Kf86ghPLD9BODEbSH27NE+RahhNZ4IHTJs7GLOjw9A0=", "e8UWjDs69lHbnwH9eVyV4K/FEhKz5dTniV3KQF71Yx8=", "LGBMVK9s7w9gir73ZFcYxpoq6skJzGNYTq2at7Lnrn4=", "5VWruvufRC0emnGps+eY0ykU2oPtF4iuTqAQpYxzFug="], "CachedAssets": {"exuXD6JeDNLxJAGlTSEFd5ml5IGVZYtqbDLB0/2z9GE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\sample-data\\weather.json", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "sample-data/weather#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iag0ou56lh", "Integrity": "enKgCMkYmCpfEcmg6Annbmc40VZ/A6aYYSQjZfVn2cU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\sample-data\\weather.json", "FileLength": 453, "LastWriteTime": "2025-07-11T23:42:16.8216669+00:00"}, "ruvIgDkeT5Ciasx/PSNYCnAvuLzs9xbQ9KZpWSR+CG8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pi1z7vwyza", "Integrity": "j0roYdzxHbKs+tsSGF+UndAuTSYVlf8UAIA7FH5RFuE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-07-11T23:42:16.7706668+00:00"}, "lxT8GwR6QKhxLHRlYYldz9panRHDXcwqrfgRxN6FEKQ=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-07-11T23:42:16.7996659+00:00"}, "ZQw4A20juVeUm6tf7XRpoxucQ+lIZShIzma/1NytemA=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mxjqfek9l7", "Integrity": "as4GtHAiy6tggXIy/Dmoru70+EGqkwoREee28Bzv8k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-07-11T23:42:16.7956661+00:00"}, "oe313YlW8C3ctbgvdAAD9PODZjyZL3VQoB8ff/35A5w=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qkwedidd64", "Integrity": "lp61DFg+flbDKom63Srg4uj7AyMb9g7lpseeR5O8fXI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-07-11T23:42:16.8116659+00:00"}, "VZGgT/pIaUMfzVlakAIr6mI1HkjkAgks20mXxHBn2k0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "w0geey6yz8", "Integrity": "oL5sVxNlE5t76B4Y6lyknP8QpP9GmQGXMxhtDTjCpbU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-07-11T23:42:16.7606672+00:00"}, "D4mUKefndNx/H14oNgFI/aUFHsVAyclopmDxcTQONRI=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-07-11T23:42:16.7366668+00:00"}, "bunvECXmCxX7i5c15CcIg4zQkx71Uye7XMKul1KoRK0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7lkir8dail", "Integrity": "RVzBLpiivASueOH/N5ckCADgF5kp6t8DRID9Y99YSiI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-07-11T23:42:16.7316658+00:00"}, "4gSOKoQmbqWQ7wPSIJFglLCuSypuFWAwriF0yiL0ehA=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "e6dc18q282", "Integrity": "SlEUZvYiRDe6JB2rK2VEV4JkriqPXn1+5SQOX4WC7P0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-07-11T23:42:16.7066664+00:00"}, "JQJiI+DNT1c4L81xx2deWzVEP6PjIvLbFqeWVRcuH0I=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9gcurp3gf0", "Integrity": "dzb8XiaS/auVUeBE6IH8MXt7RwtiYCuOtMnIgelmvcQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-07-11T23:42:16.7006647+00:00"}, "FIMIkqHcMisY5mVJS+OUq9cWq63mdL/f0hkqWg6Eubo=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-07-11T23:42:16.6746674+00:00"}, "7TK+cR9nl8NUi1G0JI8GLRVvuqE+rxAfyLzUKs0Xbqk=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j95bc3rf6q", "Integrity": "mSh6FTMhlqP+DPFhXqD6R/3ouyBdCPrQ3vgkxhDui2U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-07-11T23:42:16.6706668+00:00"}, "uPLOsYuM5Uf6BFMeKEj53jlnFGmFgndcHatpW2gnEoo=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bf7jydvgap", "Integrity": "V1Jk0foc/7JArTVwm1d76jc1QPAgnsZwU5y357Fl9n4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-07-11T23:42:16.6506669+00:00"}, "JLWnciNAQnuBverxarJLr5Pf2UvZ9Rq1W5lB4VK6rhQ=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-07-11T23:42:16.6426652+00:00"}, "URp6PeAFPepMErJbhMbSmqnhaKoOVPZIxM2+s0g/YlE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-07-11T23:42:16.6216645+00:00"}, "gKDlhuCjbLNeQXE7i8gf2X54azaVxDHcL4h+/e7OQu8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-07-11T23:42:16.613665+00:00"}, "M0dS3ywqo4Jy1S6tvrI1ogzqZ0UcFFtpsM7/urZIUB8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-07-11T23:42:16.5826866+00:00"}, "38ZrDls1abKBpvOsTR4BCnKTTHRrTeX0scndqJgNbys=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-07-11T23:42:16.5706674+00:00"}, "UqLgX+5Cu/MoT6PTiNIAJaBmZFlTZo/iVMdzfkJdvrI=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-07-11T23:42:16.5476704+00:00"}, "EF0mLuMA0D4KIrRkZLZXPZ8Z9i39J2J5n4AE+wI7LaU=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-07-11T23:42:16.5386676+00:00"}, "ytRZKxCvWGbvni2EPD4losXiJkNPxCBdRF5f1m/em6c=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-07-11T23:42:16.5056673+00:00"}, "G1lYdZHlrpKMOUnVRl0hjv/p+51UXZMhUjKZqcjj/54=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-07-11T23:42:16.4936676+00:00"}, "pAzWQlV8WorgDQfU8Ju+6e+1gTYtG+Qyt64SICun3PM=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-07-11T23:42:16.4766664+00:00"}, "OJVbFihkPnOyqBN4UXMMevP6RXFkG3uaxOwerBWB26I=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-07-11T23:42:16.472667+00:00"}, "BN92IxFHVsQjaM3XKCc6dp7w6RfrxW02U3t+Kg3422I=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-07-11T23:42:16.4240829+00:00"}, "Gckst/NVoBsTPJ40el2Ty4GFGg+T836D2HKNDfaQOTY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-07-11T23:42:16.3900478+00:00"}, "jD9EtSUYwv/IjPblv7do1WAcUeYdpEN7lvwOhPmBrLE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-07-11T23:42:17.0766668+00:00"}, "qFpL2p7W4fjQ5M0rzUBEmvfPwOzoq8k00mG/5nvVbho=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-07-11T23:42:17.0686662+00:00"}, "E7UZ6m5fQ0QoQk5aDKZ1/GoxHdkTFROcOIqsX7RJ+g4=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-07-11T23:42:17.0536691+00:00"}, "KX1H6pd+dEVeJtliNy1BrW+8m7HY6+YuYTYvYMiyUbE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-07-11T23:42:17.0356663+00:00"}, "EEIDhNwWLumIskieuQXM1komKFq0mfz67b2KquM0sQ0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-07-11T23:42:17.0306669+00:00"}, "2w0GD/4EC7LHaKZRH55MecdvA7AVBPhMTkeZQlTiaNk=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-07-11T23:42:17.0286665+00:00"}, "Fo6GiVB9PwjxaZXYq8xSIQT7QPVfcTLZPZL2NSLIyCc=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-07-11T23:42:17.0036678+00:00"}, "C/wyA0FANQ5A7nOhfHjeZXk+UtIodMFt26eOwTuoqeM=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-07-11T23:42:17.0016653+00:00"}, "hvlkYlwcAVMYXDqliNWJPszYWYB3tPgoYGBAoXRZIP8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-07-11T23:42:16.9966664+00:00"}, "v07F1s8B6GeXboeaoiRJjJ6ohSr+7pZrxF2lv2hfsdA=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-07-11T23:42:16.9956656+00:00"}, "EE5tEY0qjGadtFh8bmJniJlooyghhMY+jSTKwrIinn8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-07-11T23:42:16.9816686+00:00"}, "mTPthGEBb8vOSmK8bYrb72RhXYiq2ILKAvdzlwHN5+o=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-07-11T23:42:16.9796693+00:00"}, "hE7vvj6H39uO9LNfB6jDiuTprW1EgVC7CRqH2ihoJsQ=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-07-11T23:42:16.968669+00:00"}, "n0xz17dvuqkHCLuurDulJ9L0C8Qq12msAR8IAigg6HE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-07-11T23:42:16.9646655+00:00"}, "bPrbp/+QS9+IrqhGks0GUlE6bsX0wb3RTC54OZkLWos=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-07-11T23:42:16.9516665+00:00"}, "cQ2rY4EAdTrMdJDthcAfBEkcYMvEFbb4VUsOyAhB4yQ=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-07-11T23:42:16.9476674+00:00"}, "0XAKJ6nKGds+k9z5e66GV+3NT7E8NvMI2W8+tUh/BJU=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-07-11T23:42:16.9196663+00:00"}, "PowIZePJC8J5hPSURwSYaOpxLGpJ8LjS4HyuyLbdQ0Y=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-07-11T23:42:16.9176651+00:00"}, "GdGFZgNBFO/PkYqXDPeEDjjx6ZM4jLaQVUSYnEd2LgE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-07-11T23:42:16.8936663+00:00"}, "mOXr0prBrFIAaj/+GKrBtYaMqsUSoKjJSnEXwUYhgPE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\index.html", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ch3kc0b0ym", "Integrity": "YHWecZo9KfCSvuXg6DTnXEklNySewAp/hxCZRktURVI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 912, "LastWriteTime": "2025-07-12T08:33:58.5770926+00:00"}, "6l41Mx5jgHkkyy6sPOY8BCr0iNMIjhdeqWCzfGGjSi8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\icon-192.png", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "icon-192#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f9uvjujlxy", "Integrity": "DbpQaq68ZSb5IoPosBErM1QWBfsbTxpJqhU0REi6wP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\icon-192.png", "FileLength": 2626, "LastWriteTime": "2025-07-11T23:42:16.8746668+00:00"}, "VkC6exnX7zpFr5sMTajLSIvbhBUjWTesTczdXy6U8PM=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\favicon.png", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-07-11T23:42:16.8736671+00:00"}, "hcyGg9h/RPLjZE+NEj7faG0zeRAh+2Snke/1bvQgv7Q=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\css\\app.min.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "css/app.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vzz2p2j9p1", "Integrity": "u5PziJuUDo86ybt47eD3dbgQNUIb4zCLDM7aNN9yE18=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\app.min.css", "FileLength": 45957, "LastWriteTime": "2025-07-12T08:32:28.4867048+00:00"}, "cSUR2IbEiKk9Unhf6jOFpyvTAyGStnN0zAuoQSiLXTU=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\css\\app.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "804xk52sxk", "Integrity": "iHrbxtFCO4FM//T6csNTZCMxKvoZSjuvvTWYqRsiixw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\app.css", "FileLength": 11080, "LastWriteTime": "2025-07-12T00:02:11.7704689+00:00"}}, "CachedCopyCandidates": {}}