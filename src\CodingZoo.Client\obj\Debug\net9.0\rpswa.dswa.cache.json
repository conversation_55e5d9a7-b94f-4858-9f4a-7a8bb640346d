{"GlobalPropertiesHash": "bZPl0iaIL0t727Maz36yd9QlQSreXPdnVVIhG8E9P6o=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["OlJ2SP2TkMxKKQE9y+Ye7IUe+OuQwTYhrmjPNSHLLv8=", "TXkDz+NUk+E0fraI6mQgJiRaYwfE8XUpccssrb1UeqE=", "E9WC/fHZDwRsaQg23LvCOLMkukgO6URtJ4N/rYPnsaY=", "DhJ2K7LnkjN3+HjpvKf05E/WevDGiq7M59h64ZjAmn4=", "YXa+c4ueH0Dj1o6yP8+Hy42eEMSmxwySpwATqneetwY=", "9L12dOA7Xa7+TJk69pAd5CbynutHN99ryFcn4U56jCg=", "2XULgwITDXrtLEIaEggF37s1gj5A/SuC8+KozuSnGMc=", "6BwbyO32NtTG5xA5V6Qrn+wUpKvhEl274yzjbH9uW4Q=", "vSfHflP+e87pXyNNChHtSVj5B8yu2Owntpmwd127XcY=", "z1uMQoXMPQR9XqxlOer6mzprAJ+LF8BcxzZMxCmFU3U=", "l/3XfpqWbnYrSdSzkdjaY6s5q3qc37iehH8TO+8rNO8=", "yf1Sygi9pxBS6hay7a3iWr73EjHRr9BUqgXHfF3mBbc=", "gJ0tfDk/Qm+kJ3XfgcQJseuRSJAGDzxFIoKUVe3ilX4=", "pMvu5NXQ5IQ8SsOKCucYO+/PjGUbjH/PCF56zqBHO3Q=", "kNqw+BGQbprmUhsFoGOQGrC9IoqxpW1uei5Kdwv5zmM=", "6PPQSiHjc52Ln6B0xBCLzGTvoDMKWQ03JNPlHPnv1sw=", "9yblQ926ZXGQLNgpWnK9Crkm08P8j/GFHD2HB+v8UxY=", "cTvhPqcEOdqmhyE3uTiKaPOaT817i2qEAfsLWyWs2G8=", "aMUIZomFx7MQa9BNhI8hcw6IOOAIrPRuW91Ob4yhiSs=", "Aj9pjD53wi8II6IqzLb8ZFbbzLnQJACTP74VbGmyIRQ=", "AmHUHMI3Aw424HlskQpGUiS3obmDoaxcxstIweMyg08=", "C3xESIRbDxo7yBwlq/wf1Y5I8lHKR3PGQlsYr+oQfxc=", "cytctwNP1gEh37eBQyTILuiaNNj1co+GTaS1S13E4lA=", "nqQtpnkMT4/5BeDAG5fLrqHPHF6GqOey/RvtZZQvGbw=", "oJJvi0DJEJHDcE7nB5ZoQudXU+NW+a5sWPERy6/MGbE=", "FEeFeXlW+jCZ6QJtTEhNeZ84g9gzpZbZm5wnlKUJaxg=", "tEGOZK9iN0tqm/zNwUPhuanaFjKaK4wxq1qcvSlCxGM=", "vmDNxk+eHr1MrLEweXivj5obnaeXi8qNhfl4V0RD0oA=", "Fd7l8SpjdvtjAuWK6Nh5VcWq1Jj65FvT6Kfs2qdQO/o=", "0SDWJ2WSKMQCLPyFPKoBSFVIcL25HdXoP/PzkWhDnMM=", "AqDUBJ9JQeQJYgogLNUAXlhUs/SQYP8/GtTwAiUPkOU=", "1lfGoVAgiuosOATvwbVcLKQ9lYvSCFl4MK+TlGZwi1Y=", "gguFuPQYJ5BydZyePWkogSUnIfyh8wzw0yIDLSB/mIs=", "VMEg+ad62pgFWbv9SBLqXU+btyf9Zi1EMMcFbUzNd1g=", "GRwDkodiy39RgsdsJPcxlWK8Ex2oP0FWp9XtLYK0iRM=", "L2uB4enN+cQXwkkhRFogn3brBQRLWTb/CUcd6c5DghU=", "3y7Jvu95/Woge0ZwirBH4qy/djvdq1eMoEAyiATz/xs=", "LFq7sNHyuzeNySBoFlrAWoWAv/hdEvGy9AqAcpw5IH4=", "irOdRqLs6P1SK1uoRx8jHuOYCVPoJxepcfS4S2ZhmoY=", "abs7hv0PUQvAPwjLgV+J0lGfT8Y5uW0nC5ipJELWOm0=", "uwIP1+w4UKphpBjovSfQqEPeOoHAnvHlPjku0WHhJEA=", "0EZirGYvAFG8pr1iwFp2IS5ek0g4vmhGelYGATlb93E=", "XH5pKcKC4XrO5BRXzOrmFOjr888wFjvlVRAvHxbymbo=", "+ucrHpFF20Yojd3XG60iX8IfhXhHDrLumBxI0E1q/IA=", "GRnuvQTtAV2PsN1n+50k19jlpmfEmXgBLSylOG3nEtQ=", "XQ0rFNokW6gXJu0CyWl+jHRPmj5IJ/1BVMU7aRBkPfw=", "yxnuknKQh//DEStQtJ2ugDFxlMuEMxzAyWTay1kSOGY=", "RqYosPwUOxQWz1nayMWYxjXEIT2hn8sVrGfgoh47ud0=", "DUg5M/Jz8xYuRKncqPJexpByFGNGBTbGmJRylTTY0dc=", "f7/VrUBYP/vUIy1COgWuEM7J1BUncA2sjLSsJbNtXE8=", "SyQmdvYk6oyupoKEDPizALefPSyQXVQtaAuUJ4qO/Io=", "nMJX9uLPEiaQQqzz6yko+NKvAnJmmIW9gwCGlvfUKCI=", "qf1d6NXTnkqvE/hANuguflHfPJFhTfhj/ucp8SYRvRY=", "cQdalf+rhiOxtRwejaS+CNOdO002bfFiR6b38wMs1as=", "PmrJX2kgOy32MDokAADU0eP6vNsCo9dLJl4Eg2gQzhA=", "j2QNDvGTT0AsFQBJ+FgEFYcxbe57O/4VRk5tLy6uhbA=", "x1oi+jPAY6klodM41Gvc3cq5ROpQKFui17ik9Z7EHdA=", "BO5BWtfErGCQrVHr5Op/r7VzKH0k267HT6fhDSHrA4o=", "16tCkL0JBP6AuPR1bKxwuAYqA73H0Z3J7gEFUxoVGD0="], "CachedAssets": {"OlJ2SP2TkMxKKQE9y+Ye7IUe+OuQwTYhrmjPNSHLLv8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\css\\app.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "804xk52sxk", "Integrity": "iHrbxtFCO4FM//T6csNTZCMxKvoZSjuvvTWYqRsiixw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\app.css", "FileLength": 11080, "LastWriteTime": "2025-07-12T00:02:11.7704689+00:00"}, "YXa+c4ueH0Dj1o6yP8+Hy42eEMSmxwySpwATqneetwY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\index.html", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ch3kc0b0ym", "Integrity": "YHWecZo9KfCSvuXg6DTnXEklNySewAp/hxCZRktURVI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 912, "LastWriteTime": "2025-07-12T08:33:58.5770926+00:00"}, "E9WC/fHZDwRsaQg23LvCOLMkukgO6URtJ4N/rYPnsaY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\favicon.png", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-07-11T23:42:16.8736671+00:00"}, "DhJ2K7LnkjN3+HjpvKf05E/WevDGiq7M59h64ZjAmn4=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\icon-192.png", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "icon-192#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f9uvjujlxy", "Integrity": "DbpQaq68ZSb5IoPosBErM1QWBfsbTxpJqhU0REi6wP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\icon-192.png", "FileLength": 2626, "LastWriteTime": "2025-07-11T23:42:16.8746668+00:00"}, "TXkDz+NUk+E0fraI6mQgJiRaYwfE8XUpccssrb1UeqE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\css\\app.min.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "css/app.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vzz2p2j9p1", "Integrity": "u5PziJuUDo86ybt47eD3dbgQNUIb4zCLDM7aNN9yE18=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\app.min.css", "FileLength": 45957, "LastWriteTime": "2025-07-12T08:32:28.4867048+00:00"}, "9L12dOA7Xa7+TJk69pAd5CbynutHN99ryFcn4U56jCg=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-07-11T23:42:16.8936663+00:00"}, "2XULgwITDXrtLEIaEggF37s1gj5A/SuC8+KozuSnGMc=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-07-11T23:42:16.9176651+00:00"}, "6BwbyO32NtTG5xA5V6Qrn+wUpKvhEl274yzjbH9uW4Q=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-07-11T23:42:16.9196663+00:00"}, "vSfHflP+e87pXyNNChHtSVj5B8yu2Owntpmwd127XcY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-07-11T23:42:16.9476674+00:00"}, "z1uMQoXMPQR9XqxlOer6mzprAJ+LF8BcxzZMxCmFU3U=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-07-11T23:42:16.9516665+00:00"}, "l/3XfpqWbnYrSdSzkdjaY6s5q3qc37iehH8TO+8rNO8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-07-11T23:42:16.9646655+00:00"}, "yf1Sygi9pxBS6hay7a3iWr73EjHRr9BUqgXHfF3mBbc=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-07-11T23:42:16.968669+00:00"}, "gJ0tfDk/Qm+kJ3XfgcQJseuRSJAGDzxFIoKUVe3ilX4=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-07-11T23:42:16.9796693+00:00"}, "pMvu5NXQ5IQ8SsOKCucYO+/PjGUbjH/PCF56zqBHO3Q=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-07-11T23:42:16.9816686+00:00"}, "kNqw+BGQbprmUhsFoGOQGrC9IoqxpW1uei5Kdwv5zmM=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-07-11T23:42:16.9956656+00:00"}, "6PPQSiHjc52Ln6B0xBCLzGTvoDMKWQ03JNPlHPnv1sw=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-07-11T23:42:16.9966664+00:00"}, "9yblQ926ZXGQLNgpWnK9Crkm08P8j/GFHD2HB+v8UxY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-07-11T23:42:17.0016653+00:00"}, "cTvhPqcEOdqmhyE3uTiKaPOaT817i2qEAfsLWyWs2G8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-07-11T23:42:17.0036678+00:00"}, "aMUIZomFx7MQa9BNhI8hcw6IOOAIrPRuW91Ob4yhiSs=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-07-11T23:42:17.0286665+00:00"}, "Aj9pjD53wi8II6IqzLb8ZFbbzLnQJACTP74VbGmyIRQ=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-07-11T23:42:17.0306669+00:00"}, "AmHUHMI3Aw424HlskQpGUiS3obmDoaxcxstIweMyg08=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-07-11T23:42:17.0356663+00:00"}, "C3xESIRbDxo7yBwlq/wf1Y5I8lHKR3PGQlsYr+oQfxc=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-07-11T23:42:17.0536691+00:00"}, "cytctwNP1gEh37eBQyTILuiaNNj1co+GTaS1S13E4lA=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-07-11T23:42:17.0686662+00:00"}, "nqQtpnkMT4/5BeDAG5fLrqHPHF6GqOey/RvtZZQvGbw=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-07-11T23:42:17.0766668+00:00"}, "oJJvi0DJEJHDcE7nB5ZoQudXU+NW+a5sWPERy6/MGbE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-07-11T23:42:16.3900478+00:00"}, "FEeFeXlW+jCZ6QJtTEhNeZ84g9gzpZbZm5wnlKUJaxg=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-07-11T23:42:16.4240829+00:00"}, "tEGOZK9iN0tqm/zNwUPhuanaFjKaK4wxq1qcvSlCxGM=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-07-11T23:42:16.472667+00:00"}, "vmDNxk+eHr1MrLEweXivj5obnaeXi8qNhfl4V0RD0oA=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-07-11T23:42:16.4766664+00:00"}, "Fd7l8SpjdvtjAuWK6Nh5VcWq1Jj65FvT6Kfs2qdQO/o=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-07-11T23:42:16.4936676+00:00"}, "0SDWJ2WSKMQCLPyFPKoBSFVIcL25HdXoP/PzkWhDnMM=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-07-11T23:42:16.5056673+00:00"}, "AqDUBJ9JQeQJYgogLNUAXlhUs/SQYP8/GtTwAiUPkOU=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-07-11T23:42:16.5386676+00:00"}, "1lfGoVAgiuosOATvwbVcLKQ9lYvSCFl4MK+TlGZwi1Y=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-07-11T23:42:16.5476704+00:00"}, "gguFuPQYJ5BydZyePWkogSUnIfyh8wzw0yIDLSB/mIs=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-07-11T23:42:16.5706674+00:00"}, "VMEg+ad62pgFWbv9SBLqXU+btyf9Zi1EMMcFbUzNd1g=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-07-11T23:42:16.5826866+00:00"}, "GRwDkodiy39RgsdsJPcxlWK8Ex2oP0FWp9XtLYK0iRM=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-07-11T23:42:16.613665+00:00"}, "L2uB4enN+cQXwkkhRFogn3brBQRLWTb/CUcd6c5DghU=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-07-11T23:42:16.6216645+00:00"}, "3y7Jvu95/Woge0ZwirBH4qy/djvdq1eMoEAyiATz/xs=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-07-11T23:42:16.6426652+00:00"}, "LFq7sNHyuzeNySBoFlrAWoWAv/hdEvGy9AqAcpw5IH4=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bf7jydvgap", "Integrity": "V1Jk0foc/7JArTVwm1d76jc1QPAgnsZwU5y357Fl9n4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-07-11T23:42:16.6506669+00:00"}, "irOdRqLs6P1SK1uoRx8jHuOYCVPoJxepcfS4S2ZhmoY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j95bc3rf6q", "Integrity": "mSh6FTMhlqP+DPFhXqD6R/3ouyBdCPrQ3vgkxhDui2U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-07-11T23:42:16.6706668+00:00"}, "abs7hv0PUQvAPwjLgV+J0lGfT8Y5uW0nC5ipJELWOm0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-07-11T23:42:16.6746674+00:00"}, "uwIP1+w4UKphpBjovSfQqEPeOoHAnvHlPjku0WHhJEA=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9gcurp3gf0", "Integrity": "dzb8XiaS/auVUeBE6IH8MXt7RwtiYCuOtMnIgelmvcQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-07-11T23:42:16.7006647+00:00"}, "0EZirGYvAFG8pr1iwFp2IS5ek0g4vmhGelYGATlb93E=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "e6dc18q282", "Integrity": "SlEUZvYiRDe6JB2rK2VEV4JkriqPXn1+5SQOX4WC7P0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-07-11T23:42:16.7066664+00:00"}, "XH5pKcKC4XrO5BRXzOrmFOjr888wFjvlVRAvHxbymbo=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7lkir8dail", "Integrity": "RVzBLpiivASueOH/N5ckCADgF5kp6t8DRID9Y99YSiI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-07-11T23:42:16.7316658+00:00"}, "+ucrHpFF20Yojd3XG60iX8IfhXhHDrLumBxI0E1q/IA=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-07-11T23:42:16.7366668+00:00"}, "GRnuvQTtAV2PsN1n+50k19jlpmfEmXgBLSylOG3nEtQ=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "w0geey6yz8", "Integrity": "oL5sVxNlE5t76B4Y6lyknP8QpP9GmQGXMxhtDTjCpbU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-07-11T23:42:16.7606672+00:00"}, "XQ0rFNokW6gXJu0CyWl+jHRPmj5IJ/1BVMU7aRBkPfw=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pi1z7vwyza", "Integrity": "j0roYdzxHbKs+tsSGF+UndAuTSYVlf8UAIA7FH5RFuE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-07-11T23:42:16.7706668+00:00"}, "yxnuknKQh//DEStQtJ2ugDFxlMuEMxzAyWTay1kSOGY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mxjqfek9l7", "Integrity": "as4GtHAiy6tggXIy/Dmoru70+EGqkwoREee28Bzv8k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-07-11T23:42:16.7956661+00:00"}, "RqYosPwUOxQWz1nayMWYxjXEIT2hn8sVrGfgoh47ud0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-07-11T23:42:16.7996659+00:00"}, "DUg5M/Jz8xYuRKncqPJexpByFGNGBTbGmJRylTTY0dc=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qkwedidd64", "Integrity": "lp61DFg+flbDKom63Srg4uj7AyMb9g7lpseeR5O8fXI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-07-11T23:42:16.8116659+00:00"}, "f7/VrUBYP/vUIy1COgWuEM7J1BUncA2sjLSsJbNtXE8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\sample-data\\weather.json", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "sample-data/weather#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iag0ou56lh", "Integrity": "enKgCMkYmCpfEcmg6Annbmc40VZ/A6aYYSQjZfVn2cU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\sample-data\\weather.json", "FileLength": 453, "LastWriteTime": "2025-07-11T23:42:16.8216669+00:00"}}, "CachedCopyCandidates": {}}