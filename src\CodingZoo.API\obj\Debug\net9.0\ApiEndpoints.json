[{"ContainingType": "CodingZoo.API.Controllers.ArticlesController", "Method": "GetArticles", "RelativePath": "api/Articles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Query", "Type": "System.String", "IsRequired": false}, {"Name": "CategoryId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Tags", "Type": "System.String", "IsRequired": false}, {"Name": "IsFeatured", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsPublished", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDirection", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "CodingZoo.Shared.Common.ApiResponse`1[[CodingZoo.Shared.DTOs.PaginatedResult`1[[CodingZoo.Shared.DTOs.ArticleListDto, CodingZoo.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], CodingZoo.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "CodingZoo.API.Controllers.ArticlesController", "Method": "CreateArticle", "RelativePath": "api/Articles", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createArticleDto", "Type": "CodingZoo.Shared.DTOs.CreateArticleDto", "IsRequired": true}], "ReturnTypes": [{"Type": "CodingZoo.Shared.Common.ApiResponse`1[[CodingZoo.Shared.DTOs.ArticleDto, CodingZoo.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "CodingZoo.API.Controllers.ArticlesController", "Method": "GetArticle", "RelativePath": "api/Articles/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "CodingZoo.Shared.Common.ApiResponse`1[[CodingZoo.Shared.DTOs.ArticleDto, CodingZoo.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "CodingZoo.API.Controllers.ArticlesController", "Method": "UpdateArticle", "RelativePath": "api/Articles/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateArticleDto", "Type": "CodingZoo.Shared.DTOs.UpdateArticleDto", "IsRequired": true}], "ReturnTypes": [{"Type": "CodingZoo.Shared.Common.ApiResponse`1[[CodingZoo.Shared.DTOs.ArticleDto, CodingZoo.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "CodingZoo.API.Controllers.ArticlesController", "Method": "DeleteArticle", "RelativePath": "api/Articles/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "CodingZoo.Shared.Common.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "CodingZoo.API.Controllers.ArticlesController", "Method": "GetRelatedArticles", "RelativePath": "api/Articles/{id}/related", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "count", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "CodingZoo.Shared.Common.ApiResponse`1[[System.Collections.Generic.List`1[[CodingZoo.Shared.DTOs.ArticleListDto, CodingZoo.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "CodingZoo.API.Controllers.ArticlesController", "Method": "GetFeaturedArticles", "RelativePath": "api/Articles/featured", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "count", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "CodingZoo.Shared.Common.ApiResponse`1[[System.Collections.Generic.List`1[[CodingZoo.Shared.DTOs.ArticleListDto, CodingZoo.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "CodingZoo.API.Controllers.ArticlesController", "Method": "GetPopularArticles", "RelativePath": "api/Articles/popular", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "count", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "CodingZoo.Shared.Common.ApiResponse`1[[System.Collections.Generic.List`1[[CodingZoo.Shared.DTOs.ArticleListDto, CodingZoo.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "CodingZoo.API.Controllers.ArticlesController", "Method": "GetRecentArticles", "RelativePath": "api/Articles/recent", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "count", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "CodingZoo.Shared.Common.ApiResponse`1[[System.Collections.Generic.List`1[[CodingZoo.Shared.DTOs.ArticleListDto, CodingZoo.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "CodingZoo.API.Controllers.ArticlesController", "Method": "GetArticleBySlug", "RelativePath": "api/Articles/slug/{slug}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "slug", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "CodingZoo.Shared.Common.ApiResponse`1[[CodingZoo.Shared.DTOs.ArticleDto, CodingZoo.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "CodingZoo.API.Controllers.AuthController", "Method": "ChangePassword", "RelativePath": "api/Auth/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "changePasswordDto", "Type": "CodingZoo.Shared.DTOs.ChangePasswordDto", "IsRequired": true}], "ReturnTypes": [{"Type": "CodingZoo.Shared.Common.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "CodingZoo.API.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginDto", "Type": "CodingZoo.Shared.DTOs.LoginDto", "IsRequired": true}], "ReturnTypes": [{"Type": "CodingZoo.Shared.Common.ApiResponse`1[[CodingZoo.Shared.DTOs.LoginResponseDto, CodingZoo.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "CodingZoo.API.Controllers.AuthController", "Method": "GetProfile", "RelativePath": "api/Auth/profile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "CodingZoo.Shared.Common.ApiResponse`1[[CodingZoo.Shared.DTOs.UserDto, CodingZoo.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "CodingZoo.API.Controllers.AuthController", "Method": "UpdateProfile", "RelativePath": "api/Auth/profile", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "updateUserDto", "Type": "CodingZoo.Shared.DTOs.UpdateUserDto", "IsRequired": true}], "ReturnTypes": [{"Type": "CodingZoo.Shared.Common.ApiResponse`1[[CodingZoo.Shared.DTOs.UserDto, CodingZoo.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "CodingZoo.API.Controllers.AuthController", "Method": "Register", "RelativePath": "api/Auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createUserDto", "Type": "CodingZoo.Shared.DTOs.CreateUserDto", "IsRequired": true}], "ReturnTypes": [{"Type": "CodingZoo.Shared.Common.ApiResponse`1[[CodingZoo.Shared.DTOs.UserDto, CodingZoo.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "CodingZoo.API.Controllers.AuthController", "Method": "ValidateToken", "RelativePath": "api/Auth/validate-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "token", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "CodingZoo.Shared.Common.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "CodingZoo.API.Controllers.CategoriesController", "Method": "GetCategories", "RelativePath": "api/Categories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "CodingZoo.Shared.Common.ApiResponse`1[[System.Collections.Generic.List`1[[CodingZoo.Shared.DTOs.CategoryDto, CodingZoo.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "CodingZoo.API.Controllers.CategoriesController", "Method": "CreateCategory", "RelativePath": "api/Categories", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createCategoryDto", "Type": "CodingZoo.Shared.DTOs.CreateCategoryDto", "IsRequired": true}], "ReturnTypes": [{"Type": "CodingZoo.Shared.Common.ApiResponse`1[[CodingZoo.Shared.DTOs.CategoryDto, CodingZoo.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "CodingZoo.API.Controllers.CategoriesController", "Method": "GetCategory", "RelativePath": "api/Categories/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "CodingZoo.Shared.Common.ApiResponse`1[[CodingZoo.Shared.DTOs.CategoryDto, CodingZoo.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "CodingZoo.API.Controllers.CategoriesController", "Method": "UpdateCategory", "RelativePath": "api/Categories/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateCategoryDto", "Type": "CodingZoo.Shared.DTOs.UpdateCategoryDto", "IsRequired": true}], "ReturnTypes": [{"Type": "CodingZoo.Shared.Common.ApiResponse`1[[CodingZoo.Shared.DTOs.CategoryDto, CodingZoo.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "CodingZoo.API.Controllers.CategoriesController", "Method": "DeleteCategory", "RelativePath": "api/Categories/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "CodingZoo.Shared.Common.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "CodingZoo.API.Controllers.CategoriesController", "Method": "GetCategoryArticles", "RelativePath": "api/Categories/{id}/articles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "CodingZoo.Shared.Common.ApiResponse`1[[System.Collections.Generic.List`1[[CodingZoo.Shared.DTOs.ArticleListDto, CodingZoo.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "CodingZoo.API.Controllers.CategoriesController", "Method": "GetCategoryBySlug", "RelativePath": "api/Categories/slug/{slug}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "slug", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "CodingZoo.Shared.Common.ApiResponse`1[[CodingZoo.Shared.DTOs.CategoryDto, CodingZoo.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]