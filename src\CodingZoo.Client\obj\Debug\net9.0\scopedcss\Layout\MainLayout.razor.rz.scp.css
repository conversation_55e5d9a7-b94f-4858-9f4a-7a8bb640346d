.page[b-ryx0pz97sn] {
    position: relative;
    display: flex;
    flex-direction: column;
}

main[b-ryx0pz97sn] {
    flex: 1;
}

.sidebar[b-ryx0pz97sn] {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
}

.top-row[b-ryx0pz97sn] {
    background-color: #f7f7f7;
    border-bottom: 1px solid #d6d5d5;
    justify-content: flex-end;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

    .top-row[b-ryx0pz97sn]  a, .top-row[b-ryx0pz97sn]  .btn-link {
        white-space: nowrap;
        margin-left: 1.5rem;
        text-decoration: none;
    }

    .top-row[b-ryx0pz97sn]  a:hover, .top-row[b-ryx0pz97sn]  .btn-link:hover {
        text-decoration: underline;
    }

    .top-row[b-ryx0pz97sn]  a:first-child {
        overflow: hidden;
        text-overflow: ellipsis;
    }

@media (max-width: 640.98px) {
    .top-row[b-ryx0pz97sn] {
        justify-content: space-between;
    }

    .top-row[b-ryx0pz97sn]  a, .top-row[b-ryx0pz97sn]  .btn-link {
        margin-left: 0;
    }
}

@media (min-width: 641px) {
    .page[b-ryx0pz97sn] {
        flex-direction: row;
    }

    .sidebar[b-ryx0pz97sn] {
        width: 250px;
        height: 100vh;
        position: sticky;
        top: 0;
    }

    .top-row[b-ryx0pz97sn] {
        position: sticky;
        top: 0;
        z-index: 1;
    }

    .top-row.auth[b-ryx0pz97sn]  a:first-child {
        flex: 1;
        text-align: right;
        width: 0;
    }

    .top-row[b-ryx0pz97sn], article[b-ryx0pz97sn] {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }
}
