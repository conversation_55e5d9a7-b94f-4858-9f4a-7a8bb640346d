using System.ComponentModel.DataAnnotations;

namespace CodingZoo.Shared.DTOs;

public class CategoryDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Slug { get; set; } = string.Empty;
    public string Icon { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;
    public int SortOrder { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public int ArticleCount { get; set; }
}

public class CreateCategoryDto
{
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [StringLength(200)]
    public string Description { get; set; } = string.Empty;
    
    [StringLength(100)]
    public string Icon { get; set; } = string.Empty;
    
    [StringLength(20)]
    public string Color { get; set; } = string.Empty;
    
    public int SortOrder { get; set; }
    public bool IsActive { get; set; } = true;
}

public class UpdateCategoryDto
{
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [StringLength(200)]
    public string Description { get; set; } = string.Empty;
    
    [StringLength(100)]
    public string Icon { get; set; } = string.Empty;
    
    [StringLength(20)]
    public string Color { get; set; } = string.Empty;
    
    public int SortOrder { get; set; }
    public bool IsActive { get; set; } = true;
}
