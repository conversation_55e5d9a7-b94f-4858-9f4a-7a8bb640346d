@page "/categories"
@using CodingZoo.Client.Services
@using CodingZoo.Shared.DTOs
@inject ICategoryService CategoryService

<PageTitle>Categories - CodingZoo</PageTitle>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Categories
        </h1>
        <p class="text-lg text-gray-600 dark:text-gray-400">
            Browse articles by programming language and technology category.
        </p>
    </div>

    <!-- Categories Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        @if (categories != null && categories.Any())
        {
            @foreach (var category in categories)
            {
                <div class="card-hover group cursor-pointer">
                    <div class="card-body text-center">
                        <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-200 dark:group-hover:bg-primary-800 transition-colors">
                            <i class="devicon-@(category.Name.ToLower())-plain text-2xl text-primary-600 dark:text-primary-400"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                            @category.Name
                        </h3>
                        <p class="text-gray-600 dark:text-gray-400 mb-4">
                            @category.Description
                        </p>
                        <div class="text-sm text-gray-500 dark:text-gray-500">
                            @category.ArticleCount articles
                        </div>
                    </div>
                </div>
            }
        }
        else
        {
            @for (int i = 0; i < 8; i++)
            {
                <div class="card">
                    <div class="card-body text-center">
                        <div class="w-16 h-16 skeleton rounded-full mx-auto mb-4"></div>
                        <div class="skeleton-title mb-2"></div>
                        <div class="skeleton-text mb-4"></div>
                        <div class="skeleton-text w-1/2 mx-auto"></div>
                    </div>
                </div>
            }
        }
    </div>
</div>

@code {
    private List<CategoryDto>? categories;
    private bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadCategoriesAsync();
    }

    private async Task LoadCategoriesAsync()
    {
        try
        {
            isLoading = true;
            
            var response = await CategoryService.GetCategoriesAsync();
            if (response?.Success == true)
            {
                categories = response.Data;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading categories: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }
}
