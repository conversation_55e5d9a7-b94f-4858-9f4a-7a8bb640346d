using CodingZoo.Shared.DTOs;
using CodingZoo.Shared.Common;

namespace CodingZoo.Client.Services;

public class CategoryService : ICategoryService
{
    private readonly IApiService _apiService;

    public CategoryService(IApiService apiService)
    {
        _apiService = apiService;
    }

    public async Task<ApiResponse<List<CategoryDto>>?> GetCategoriesAsync()
    {
        return await _apiService.GetAsync<List<CategoryDto>>("api/categories");
    }

    public async Task<ApiResponse<CategoryDto>?> GetCategoryAsync(int id)
    {
        return await _apiService.GetAsync<CategoryDto>($"api/categories/{id}");
    }

    public async Task<ApiResponse<CategoryDto>?> GetCategoryBySlugAsync(string slug)
    {
        return await _apiService.GetAsync<CategoryDto>($"api/categories/slug/{slug}");
    }

    public async Task<ApiResponse<List<ArticleListDto>>?> GetCategoryArticlesAsync(int categoryId, int page = 1, int pageSize = 10)
    {
        return await _apiService.GetAsync<List<ArticleListDto>>($"api/categories/{categoryId}/articles?page={page}&pageSize={pageSize}");
    }

    public async Task<ApiResponse<CategoryDto>?> CreateCategoryAsync(CreateCategoryDto createCategoryDto)
    {
        return await _apiService.PostAsync<CategoryDto>("api/categories", createCategoryDto);
    }

    public async Task<ApiResponse<CategoryDto>?> UpdateCategoryAsync(int id, UpdateCategoryDto updateCategoryDto)
    {
        return await _apiService.PutAsync<CategoryDto>($"api/categories/{id}", updateCategoryDto);
    }

    public async Task<ApiResponse?> DeleteCategoryAsync(int id)
    {
        return await _apiService.DeleteAsync($"api/categories/{id}");
    }
}
