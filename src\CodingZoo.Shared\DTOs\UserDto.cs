using System.ComponentModel.DataAnnotations;

namespace CodingZoo.Shared.DTOs;

public class UserDto
{
    public int Id { get; set; }
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Username { get; set; } = string.Empty;
    public string Avatar { get; set; } = string.Empty;
    public string Bio { get; set; } = string.Empty;
    public string Website { get; set; } = string.Empty;
    public string TwitterHandle { get; set; } = string.Empty;
    public string GitHubHandle { get; set; } = string.Empty;
    public bool IsAdmin { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastLoginAt { get; set; }
    public string FullName => $"{FirstName} {LastName}".Trim();
}

public class CreateUserDto
{
    [Required]
    [StringLength(100)]
    public string FirstName { get; set; } = string.Empty;
    
    [Required]
    [StringLength(100)]
    public string LastName { get; set; } = string.Empty;
    
    [Required]
    [StringLength(200)]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
    
    [Required]
    [StringLength(100)]
    public string Username { get; set; } = string.Empty;
    
    [Required]
    [StringLength(100, MinimumLength = 6)]
    public string Password { get; set; } = string.Empty;
    
    [StringLength(500)]
    public string Bio { get; set; } = string.Empty;
    
    [StringLength(100)]
    public string Website { get; set; } = string.Empty;
    
    [StringLength(100)]
    public string TwitterHandle { get; set; } = string.Empty;
    
    [StringLength(100)]
    public string GitHubHandle { get; set; } = string.Empty;
    
    public bool IsAdmin { get; set; }
}

public class UpdateUserDto
{
    [Required]
    [StringLength(100)]
    public string FirstName { get; set; } = string.Empty;
    
    [Required]
    [StringLength(100)]
    public string LastName { get; set; } = string.Empty;
    
    [Required]
    [StringLength(200)]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
    
    [Required]
    [StringLength(100)]
    public string Username { get; set; } = string.Empty;
    
    [StringLength(200)]
    public string Avatar { get; set; } = string.Empty;
    
    [StringLength(500)]
    public string Bio { get; set; } = string.Empty;
    
    [StringLength(100)]
    public string Website { get; set; } = string.Empty;
    
    [StringLength(100)]
    public string TwitterHandle { get; set; } = string.Empty;
    
    [StringLength(100)]
    public string GitHubHandle { get; set; } = string.Empty;
    
    public bool IsAdmin { get; set; }
    public bool IsActive { get; set; }
}

public class LoginDto
{
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
    
    [Required]
    public string Password { get; set; } = string.Empty;
    
    public bool RememberMe { get; set; }
}

public class LoginResponseDto
{
    public string Token { get; set; } = string.Empty;
    public UserDto User { get; set; } = null!;
    public DateTime ExpiresAt { get; set; }
}

public class ChangePasswordDto
{
    [Required]
    public string CurrentPassword { get; set; } = string.Empty;
    
    [Required]
    [StringLength(100, MinimumLength = 6)]
    public string NewPassword { get; set; } = string.Empty;
    
    [Required]
    [Compare("NewPassword")]
    public string ConfirmPassword { get; set; } = string.Empty;
}
