using Microsoft.EntityFrameworkCore;
using CodingZoo.API.Data;
using CodingZoo.Shared.Models;

namespace CodingZoo.API.Repositories;

public class CategoryRepository : Repository<Category>, ICategoryRepository
{
    public CategoryRepository(CodingZooDbContext context) : base(context)
    {
    }

    public async Task<Category?> GetCategoryBySlugAsync(string slug)
    {
        return await _dbSet.FirstOrDefaultAsync(c => c.Slug == slug);
    }

    public async Task<IEnumerable<Category>> GetActiveCategoriesAsync()
    {
        return await _dbSet.Where(c => c.IsActive)
                          .OrderBy(c => c.SortOrder)
                          .ThenBy(c => c.Name)
                          .ToListAsync();
    }

    public async Task<bool> IsSlugUniqueAsync(string slug, int? excludeId = null)
    {
        var query = _dbSet.Where(c => c.Slug == slug);
        if (excludeId.HasValue)
        {
            query = query.Where(c => c.Id != excludeId.Value);
        }
        return !await query.AnyAsync();
    }

    public async Task<Dictionary<int, int>> GetArticleCountsByCategoryAsync()
    {
        return await _context.Articles
            .Where(a => a.IsPublished)
            .GroupBy(a => a.CategoryId)
            .ToDictionaryAsync(g => g.Key, g => g.Count());
    }
}
