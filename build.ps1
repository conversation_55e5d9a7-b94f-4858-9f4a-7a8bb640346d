# CodingZoo Build Script
# This script builds the entire CodingZoo application

Write-Host "🦁 Building CodingZoo - Your Ultimate Coding Education Platform" -ForegroundColor Green
Write-Host "Powered by ArslanDevs" -ForegroundColor Cyan
Write-Host ""

# Check if .NET is installed
try {
    $dotnetVersion = dotnet --version
    Write-Host "✅ .NET SDK Version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ .NET SDK not found. Please install .NET 9.0 SDK" -ForegroundColor Red
    exit 1
}

# Check if Node.js is installed
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js Version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js not found. Please install Node.js" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🔧 Building the application..." -ForegroundColor Yellow

# Restore .NET packages
Write-Host "📦 Restoring .NET packages..." -ForegroundColor Blue
dotnet restore
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to restore .NET packages" -ForegroundColor Red
    exit 1
}

# Install Node.js packages for Tailwind CSS
Write-Host "📦 Installing Node.js packages..." -ForegroundColor Blue
Set-Location "src/CodingZoo.Client"
npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to install Node.js packages" -ForegroundColor Red
    exit 1
}

# Build Tailwind CSS
Write-Host "🎨 Building Tailwind CSS..." -ForegroundColor Blue
npm run build-css-prod
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to build Tailwind CSS" -ForegroundColor Red
    exit 1
}

# Return to root directory
Set-Location "../.."

# Build the solution
Write-Host "🏗️ Building the solution..." -ForegroundColor Blue
dotnet build --configuration Release
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to build the solution" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "✅ Build completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "🚀 To run the application:" -ForegroundColor Cyan
Write-Host "   1. Start the API: cd src/CodingZoo.API && dotnet run" -ForegroundColor White
Write-Host "   2. Start the Client: cd src/CodingZoo.Client && dotnet run" -ForegroundColor White
Write-Host ""
Write-Host "📚 API Documentation: https://localhost:7000/swagger" -ForegroundColor White
Write-Host "🌐 Client Application: https://localhost:7001" -ForegroundColor White
Write-Host ""
Write-Host "Happy coding! 🦁" -ForegroundColor Green
