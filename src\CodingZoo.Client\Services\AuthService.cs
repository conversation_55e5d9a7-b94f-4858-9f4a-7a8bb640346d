using Microsoft.JSInterop;
using CodingZoo.Shared.DTOs;
using CodingZoo.Shared.Common;
using System.Text.Json;

namespace CodingZoo.Client.Services;

public class AuthService : IAuthService
{
    private readonly IApiService _apiService;
    private readonly IJSRuntime _jsRuntime;
    private readonly JsonSerializerOptions _jsonOptions;
    private UserDto? _currentUser;
    private string? _currentToken;

    public AuthService(IApiService apiService, IJSRuntime jsRuntime)
    {
        _apiService = apiService;
        _jsRuntime = jsRuntime;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };
    }

    public async Task<ApiResponse<LoginResponseDto>?> LoginAsync(LoginDto loginDto)
    {
        var response = await _apiService.PostAsync<LoginResponseDto>("api/auth/login", loginDto);
        
        if (response?.Success == true && response.Data != null)
        {
            _currentToken = response.Data.Token;
            _currentUser = response.Data.User;
            
            // Store token in localStorage
            await _jsRuntime.InvokeVoidAsync("localStorage.setItem", "authToken", _currentToken);
            await _jsRuntime.InvokeVoidAsync("localStorage.setItem", "currentUser", JsonSerializer.Serialize(_currentUser, _jsonOptions));
            
            // Set auth header for future requests
            _apiService.SetAuthToken(_currentToken);
        }
        
        return response;
    }

    public async Task<ApiResponse<UserDto>?> RegisterAsync(CreateUserDto createUserDto)
    {
        return await _apiService.PostAsync<UserDto>("api/auth/register", createUserDto);
    }

    public async Task<ApiResponse<UserDto>?> GetProfileAsync()
    {
        return await _apiService.GetAsync<UserDto>("api/auth/profile");
    }

    public async Task<ApiResponse<UserDto>?> UpdateProfileAsync(UpdateUserDto updateUserDto)
    {
        var response = await _apiService.PutAsync<UserDto>("api/auth/profile", updateUserDto);
        
        if (response?.Success == true && response.Data != null)
        {
            _currentUser = response.Data;
            await _jsRuntime.InvokeVoidAsync("localStorage.setItem", "currentUser", JsonSerializer.Serialize(_currentUser, _jsonOptions));
        }
        
        return response;
    }

    public async Task<ApiResponse?> ChangePasswordAsync(ChangePasswordDto changePasswordDto)
    {
        return await _apiService.PostAsync<object>("api/auth/change-password", changePasswordDto);
    }

    public async Task<ApiResponse<bool>?> ValidateTokenAsync(string token)
    {
        return await _apiService.PostAsync<bool>("api/auth/validate-token", token);
    }

    public async Task LogoutAsync()
    {
        _currentToken = null;
        _currentUser = null;
        
        // Clear localStorage
        await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", "authToken");
        await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", "currentUser");
        
        // Clear auth header
        _apiService.ClearAuthToken();
    }

    public async Task<bool> IsAuthenticatedAsync()
    {
        if (_currentToken != null)
            return true;
            
        // Check localStorage
        try
        {
            var token = await _jsRuntime.InvokeAsync<string?>("localStorage.getItem", "authToken");
            if (!string.IsNullOrEmpty(token))
            {
                // Validate token with server
                var validationResponse = await ValidateTokenAsync(token);
                if (validationResponse?.Success == true && validationResponse.Data == true)
                {
                    _currentToken = token;
                    _apiService.SetAuthToken(token);
                    
                    // Load user data
                    var userJson = await _jsRuntime.InvokeAsync<string?>("localStorage.getItem", "currentUser");
                    if (!string.IsNullOrEmpty(userJson))
                    {
                        _currentUser = JsonSerializer.Deserialize<UserDto>(userJson, _jsonOptions);
                    }
                    
                    return true;
                }
                else
                {
                    // Token is invalid, clear it
                    await LogoutAsync();
                }
            }
        }
        catch
        {
            // Error accessing localStorage or validating token
        }
        
        return false;
    }

    public async Task<string?> GetTokenAsync()
    {
        if (_currentToken != null)
            return _currentToken;
            
        try
        {
            _currentToken = await _jsRuntime.InvokeAsync<string?>("localStorage.getItem", "authToken");
            if (!string.IsNullOrEmpty(_currentToken))
            {
                _apiService.SetAuthToken(_currentToken);
            }
        }
        catch
        {
            // Error accessing localStorage
        }
        
        return _currentToken;
    }

    public async Task<UserDto?> GetCurrentUserAsync()
    {
        if (_currentUser != null)
            return _currentUser;
            
        try
        {
            var userJson = await _jsRuntime.InvokeAsync<string?>("localStorage.getItem", "currentUser");
            if (!string.IsNullOrEmpty(userJson))
            {
                _currentUser = JsonSerializer.Deserialize<UserDto>(userJson, _jsonOptions);
            }
        }
        catch
        {
            // Error accessing localStorage or deserializing user
        }
        
        return _currentUser;
    }
}
