# CodingZoo Development Server Script
# This script starts both the API and Client in development mode

Write-Host "🦁 Starting CodingZoo Development Servers" -ForegroundColor Green
Write-Host "Powered by Arslan<PERSON>evs" -ForegroundColor Cyan
Write-Host ""

# Function to start a process in a new window
function Start-DevServer {
    param(
        [string]$Title,
        [string]$Path,
        [string]$Command
    )
    
    Write-Host "🚀 Starting $Title..." -ForegroundColor Blue
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$Path'; $Command" -WindowStyle Normal
}

# Start the API server
Start-DevServer -Title "CodingZoo API" -Path "src/CodingZoo.API" -Command "dotnet run"

# Wait a moment for the API to start
Start-Sleep -Seconds 3

# Start the Blazor client
Start-DevServer -Title "CodingZoo Client" -Path "src/CodingZoo.Client" -Command "dotnet run"

# Start Tailwind CSS watcher
Start-DevServer -Title "Tailwind CSS Watcher" -Path "src/CodingZoo.Client" -Command "npm run build-css"

Write-Host ""
Write-Host "✅ Development servers are starting..." -ForegroundColor Green
Write-Host ""
Write-Host "📚 API Documentation: https://localhost:7000/swagger" -ForegroundColor White
Write-Host "🌐 Client Application: https://localhost:7001" -ForegroundColor White
Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
