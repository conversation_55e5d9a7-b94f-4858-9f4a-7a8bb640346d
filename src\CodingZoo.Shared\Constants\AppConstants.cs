namespace CodingZoo.Shared.Constants;

public static class AppConstants
{
    public const string AppName = "CodingZoo";
    public const string AppDescription = "Your Ultimate Coding Education Platform";
    public const string PoweredBy = "Powered by ArslanDevs";
    
    public static class Roles
    {
        public const string Admin = "Admin";
        public const string User = "User";
    }
    
    public static class Claims
    {
        public const string UserId = "user_id";
        public const string Email = "email";
        public const string Role = "role";
        public const string Username = "username";
    }
    
    public static class CacheKeys
    {
        public const string Categories = "categories";
        public const string FeaturedArticles = "featured_articles";
        public const string PopularArticles = "popular_articles";
        public const string RecentArticles = "recent_articles";
    }
    
    public static class FileUpload
    {
        public const int MaxFileSizeBytes = 5 * 1024 * 1024; // 5MB
        public static readonly string[] AllowedImageTypes = { "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp" };
        public static readonly string[] AllowedDocumentTypes = { "application/pdf", "text/plain", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document" };
    }
    
    public static class Pagination
    {
        public const int DefaultPageSize = 10;
        public const int MaxPageSize = 50;
    }
    
    public static class PopularLanguages
    {
        public static readonly List<LanguageInfo> Languages = new()
        {
            new("C#", "csharp", "#239120", "devicon-csharp-plain"),
            new("Python", "python", "#3776AB", "devicon-python-plain"),
            new("JavaScript", "javascript", "#F7DF1E", "devicon-javascript-plain"),
            new("Java", "java", "#ED8B00", "devicon-java-plain"),
            new("React", "react", "#61DAFB", "devicon-react-original"),
            new("Node.js", "nodejs", "#339933", "devicon-nodejs-plain"),
            new("TypeScript", "typescript", "#3178C6", "devicon-typescript-plain"),
            new("Angular", "angular", "#DD0031", "devicon-angularjs-plain"),
            new("Vue.js", "vuejs", "#4FC08D", "devicon-vuejs-plain"),
            new("PHP", "php", "#777BB4", "devicon-php-plain"),
            new("Go", "go", "#00ADD8", "devicon-go-plain"),
            new("Rust", "rust", "#000000", "devicon-rust-plain")
        };
    }
    
    public record LanguageInfo(string Name, string Slug, string Color, string Icon);
}
