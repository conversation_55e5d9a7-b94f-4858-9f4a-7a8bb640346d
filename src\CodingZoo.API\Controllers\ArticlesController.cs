using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AutoMapper;
using CodingZoo.API.Repositories;
using CodingZoo.Shared.Models;
using CodingZoo.Shared.DTOs;
using CodingZoo.Shared.Common;
using CodingZoo.Shared.Constants;
using System.Security.Claims;

namespace CodingZoo.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ArticlesController : ControllerBase
{
    private readonly IArticleRepository _articleRepository;
    private readonly ICategoryRepository _categoryRepository;
    private readonly IMapper _mapper;

    public ArticlesController(
        IArticleRepository articleRepository,
        ICategoryRepository categoryRepository,
        IMapper mapper)
    {
        _articleRepository = articleRepository;
        _categoryRepository = categoryRepository;
        _mapper = mapper;
    }

    [HttpGet]
    public async Task<ActionResult<ApiResponse<PaginatedResult<ArticleListDto>>>> GetArticles([FromQuery] ArticleSearchDto searchDto)
    {
        try
        {
            var (articles, totalCount) = await _articleRepository.SearchArticlesAsync(searchDto);
            var articleDtos = _mapper.Map<List<ArticleListDto>>(articles);

            var result = new PaginatedResult<ArticleListDto>
            {
                Items = articleDtos,
                TotalCount = totalCount,
                Page = searchDto.Page,
                PageSize = searchDto.PageSize
            };

            return Ok(ApiResponse<PaginatedResult<ArticleListDto>>.SuccessResult(result));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<PaginatedResult<ArticleListDto>>.ErrorResult("An error occurred while retrieving articles."));
        }
    }

    [HttpGet("{id:int}")]
    public async Task<ActionResult<ApiResponse<ArticleDto>>> GetArticle(int id)
    {
        try
        {
            var article = await _articleRepository.GetByIdAsync(id);
            if (article == null)
            {
                return NotFound(ApiResponse<ArticleDto>.ErrorResult("Article not found."));
            }

            // Increment view count
            await _articleRepository.IncrementViewCountAsync(id);

            var articleDto = _mapper.Map<ArticleDto>(article);
            return Ok(ApiResponse<ArticleDto>.SuccessResult(articleDto));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<ArticleDto>.ErrorResult("An error occurred while retrieving the article."));
        }
    }

    [HttpGet("slug/{slug}")]
    public async Task<ActionResult<ApiResponse<ArticleDto>>> GetArticleBySlug(string slug)
    {
        try
        {
            var article = await _articleRepository.GetArticleBySlugAsync(slug);
            if (article == null)
            {
                return NotFound(ApiResponse<ArticleDto>.ErrorResult("Article not found."));
            }

            // Increment view count
            await _articleRepository.IncrementViewCountAsync(article.Id);

            var articleDto = _mapper.Map<ArticleDto>(article);
            return Ok(ApiResponse<ArticleDto>.SuccessResult(articleDto));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<ArticleDto>.ErrorResult("An error occurred while retrieving the article."));
        }
    }

    [HttpGet("featured")]
    public async Task<ActionResult<ApiResponse<List<ArticleListDto>>>> GetFeaturedArticles([FromQuery] int count = 6)
    {
        try
        {
            var articles = await _articleRepository.GetFeaturedArticlesAsync(count);
            var articleDtos = _mapper.Map<List<ArticleListDto>>(articles);
            return Ok(ApiResponse<List<ArticleListDto>>.SuccessResult(articleDtos));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<List<ArticleListDto>>.ErrorResult("An error occurred while retrieving featured articles."));
        }
    }

    [HttpGet("recent")]
    public async Task<ActionResult<ApiResponse<List<ArticleListDto>>>> GetRecentArticles([FromQuery] int count = 10)
    {
        try
        {
            var articles = await _articleRepository.GetRecentArticlesAsync(count);
            var articleDtos = _mapper.Map<List<ArticleListDto>>(articles);
            return Ok(ApiResponse<List<ArticleListDto>>.SuccessResult(articleDtos));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<List<ArticleListDto>>.ErrorResult("An error occurred while retrieving recent articles."));
        }
    }

    [HttpGet("popular")]
    public async Task<ActionResult<ApiResponse<List<ArticleListDto>>>> GetPopularArticles([FromQuery] int count = 10)
    {
        try
        {
            var articles = await _articleRepository.GetPopularArticlesAsync(count);
            var articleDtos = _mapper.Map<List<ArticleListDto>>(articles);
            return Ok(ApiResponse<List<ArticleListDto>>.SuccessResult(articleDtos));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<List<ArticleListDto>>.ErrorResult("An error occurred while retrieving popular articles."));
        }
    }

    [HttpGet("{id:int}/related")]
    public async Task<ActionResult<ApiResponse<List<ArticleListDto>>>> GetRelatedArticles(int id, [FromQuery] int count = 5)
    {
        try
        {
            var articles = await _articleRepository.GetRelatedArticlesAsync(id, count);
            var articleDtos = _mapper.Map<List<ArticleListDto>>(articles);
            return Ok(ApiResponse<List<ArticleListDto>>.SuccessResult(articleDtos));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<List<ArticleListDto>>.ErrorResult("An error occurred while retrieving related articles."));
        }
    }

    [HttpPost]
    [Authorize(Roles = AppConstants.Roles.Admin)]
    public async Task<ActionResult<ApiResponse<ArticleDto>>> CreateArticle([FromBody] CreateArticleDto createArticleDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                return BadRequest(ApiResponse<ArticleDto>.ErrorResult(errors));
            }

            // Get current user ID from claims
            var userIdClaim = User.FindFirst(AppConstants.Claims.UserId);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int authorId))
            {
                return Unauthorized(ApiResponse<ArticleDto>.ErrorResult("Invalid user token."));
            }

            // Verify category exists
            var category = await _categoryRepository.GetByIdAsync(createArticleDto.CategoryId);
            if (category == null)
            {
                return BadRequest(ApiResponse<ArticleDto>.ErrorResult("Invalid category."));
            }

            var article = _mapper.Map<Article>(createArticleDto);
            article.AuthorId = authorId;
            article.Slug = GenerateSlug(createArticleDto.Title);
            article.CreatedAt = DateTime.UtcNow;
            article.UpdatedAt = DateTime.UtcNow;
            
            if (createArticleDto.IsPublished)
            {
                article.PublishedAt = DateTime.UtcNow;
            }

            // Ensure slug is unique
            var originalSlug = article.Slug;
            var counter = 1;
            while (!await _articleRepository.IsSlugUniqueAsync(article.Slug))
            {
                article.Slug = $"{originalSlug}-{counter}";
                counter++;
            }

            var createdArticle = await _articleRepository.AddAsync(article);
            var articleDto = _mapper.Map<ArticleDto>(createdArticle);

            return CreatedAtAction(nameof(GetArticle), new { id = createdArticle.Id }, 
                ApiResponse<ArticleDto>.SuccessResult(articleDto, "Article created successfully."));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<ArticleDto>.ErrorResult("An error occurred while creating the article."));
        }
    }

    [HttpPut("{id:int}")]
    [Authorize(Roles = AppConstants.Roles.Admin)]
    public async Task<ActionResult<ApiResponse<ArticleDto>>> UpdateArticle(int id, [FromBody] UpdateArticleDto updateArticleDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                return BadRequest(ApiResponse<ArticleDto>.ErrorResult(errors));
            }

            var existingArticle = await _articleRepository.GetByIdAsync(id);
            if (existingArticle == null)
            {
                return NotFound(ApiResponse<ArticleDto>.ErrorResult("Article not found."));
            }

            // Verify category exists
            var category = await _categoryRepository.GetByIdAsync(updateArticleDto.CategoryId);
            if (category == null)
            {
                return BadRequest(ApiResponse<ArticleDto>.ErrorResult("Invalid category."));
            }

            // Update article properties
            _mapper.Map(updateArticleDto, existingArticle);
            existingArticle.UpdatedAt = DateTime.UtcNow;

            // Update slug if title changed
            var newSlug = GenerateSlug(updateArticleDto.Title);
            if (existingArticle.Slug != newSlug)
            {
                // Ensure new slug is unique
                var originalSlug = newSlug;
                var counter = 1;
                while (!await _articleRepository.IsSlugUniqueAsync(newSlug, id))
                {
                    newSlug = $"{originalSlug}-{counter}";
                    counter++;
                }
                existingArticle.Slug = newSlug;
            }

            // Update published date if publishing for the first time
            if (updateArticleDto.IsPublished && !existingArticle.IsPublished)
            {
                existingArticle.PublishedAt = DateTime.UtcNow;
            }

            await _articleRepository.UpdateAsync(existingArticle);
            var articleDto = _mapper.Map<ArticleDto>(existingArticle);

            return Ok(ApiResponse<ArticleDto>.SuccessResult(articleDto, "Article updated successfully."));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<ArticleDto>.ErrorResult("An error occurred while updating the article."));
        }
    }

    [HttpDelete("{id:int}")]
    [Authorize(Roles = AppConstants.Roles.Admin)]
    public async Task<ActionResult<ApiResponse>> DeleteArticle(int id)
    {
        try
        {
            var article = await _articleRepository.GetByIdAsync(id);
            if (article == null)
            {
                return NotFound(ApiResponse.ErrorResult("Article not found."));
            }

            await _articleRepository.DeleteAsync(article);
            return Ok(ApiResponse.SuccessResult("Article deleted successfully."));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse.ErrorResult("An error occurred while deleting the article."));
        }
    }

    private static string GenerateSlug(string title)
    {
        return title.ToLowerInvariant()
            .Replace(" ", "-")
            .Replace(".", "")
            .Replace(",", "")
            .Replace("!", "")
            .Replace("?", "")
            .Replace(":", "")
            .Replace(";", "")
            .Replace("'", "")
            .Replace("\"", "")
            .Replace("(", "")
            .Replace(")", "")
            .Replace("[", "")
            .Replace("]", "")
            .Replace("{", "")
            .Replace("}", "")
            .Replace("/", "-")
            .Replace("\\", "-")
            .Replace("&", "and");
    }
}
