using Microsoft.AspNetCore.Components;

namespace CodingZoo.Client.Layout;

public partial class MainLayout : LayoutComponentBase
{
    private bool isDarkMode;
    private bool showMobileMenu;

    protected override async Task OnInitializedAsync()
    {
        isDarkMode = await ThemeService.GetIsDarkModeAsync();
        ThemeService.ThemeChanged += OnThemeChanged;
    }

    private void OnThemeChanged(bool darkMode)
    {
        isDarkMode = darkMode;
        InvokeAsync(StateHasChanged);
    }

    private async Task ToggleTheme()
    {
        await ThemeService.ToggleDarkModeAsync();
    }

    private void ToggleMobileMenu()
    {
        showMobileMenu = !showMobileMenu;
    }

    private void OpenSearch()
    {
        // TODO: Implement search modal
    }

    public void Dispose()
    {
        ThemeService.ThemeChanged -= OnThemeChanged;
    }
}
