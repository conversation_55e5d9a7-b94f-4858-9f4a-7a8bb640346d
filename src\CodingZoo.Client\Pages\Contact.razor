@page "/contact"

<PageTitle>Contact - Coding<PERSON>oo</PageTitle>

<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="text-center mb-12">
        <h1 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Contact Us
        </h1>
        <p class="text-xl text-gray-600 dark:text-gray-400">
            We'd love to hear from you. Get in touch with the CodingZoo team.
        </p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        <!-- Contact Form -->
        <div class="card">
            <div class="card-body">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Send us a Message</h2>
                <form class="space-y-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Full Name
                        </label>
                        <input type="text" id="name" name="name" required
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-800 dark:text-white" />
                    </div>
                    
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Email Address
                        </label>
                        <input type="email" id="email" name="email" required
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-800 dark:text-white" />
                    </div>
                    
                    <div>
                        <label for="subject" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Subject
                        </label>
                        <select id="subject" name="subject" required
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-800 dark:text-white">
                            <option value="">Select a subject</option>
                            <option value="general">General Inquiry</option>
                            <option value="tutorial">Tutorial Request</option>
                            <option value="bug">Bug Report</option>
                            <option value="partnership">Partnership</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Message
                        </label>
                        <textarea id="message" name="message" rows="6" required
                                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                                  placeholder="Tell us how we can help you..."></textarea>
                    </div>
                    
                    <button type="submit" class="btn-primary w-full">
                        Send Message
                    </button>
                </form>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="space-y-8">
            <div class="card">
                <div class="card-body">
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">Get in Touch</h3>
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mt-1">
                                <i class="fas fa-envelope text-primary-600 dark:text-primary-400 text-sm"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-900 dark:text-white">Email</div>
                                <div class="text-gray-600 dark:text-gray-400"><EMAIL></div>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mt-1">
                                <i class="fas fa-clock text-primary-600 dark:text-primary-400 text-sm"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-900 dark:text-white">Response Time</div>
                                <div class="text-gray-600 dark:text-gray-400">Within 24 hours</div>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mt-1">
                                <i class="fas fa-globe text-primary-600 dark:text-primary-400 text-sm"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-900 dark:text-white">Website</div>
                                <div class="text-gray-600 dark:text-gray-400">www.codingzoo.com</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">Follow Us</h3>
                    <div class="flex space-x-4">
                        <a href="#" class="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center hover:bg-primary-200 dark:hover:bg-primary-800 transition-colors">
                            <i class="fab fa-twitter text-primary-600 dark:text-primary-400"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center hover:bg-primary-200 dark:hover:bg-primary-800 transition-colors">
                            <i class="fab fa-github text-primary-600 dark:text-primary-400"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center hover:bg-primary-200 dark:hover:bg-primary-800 transition-colors">
                            <i class="fab fa-linkedin text-primary-600 dark:text-primary-400"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center hover:bg-primary-200 dark:hover:bg-primary-800 transition-colors">
                            <i class="fab fa-youtube text-primary-600 dark:text-primary-400"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="card bg-gradient-to-r from-primary-600 to-accent-600 dark:from-primary-800 dark:to-accent-800">
                <div class="card-body text-white">
                    <h3 class="text-xl font-bold mb-4">Powered by ArslanDevs</h3>
                    <p class="text-primary-100 mb-4">
                        CodingZoo is developed and maintained by ArslanDevs, committed to providing 
                        the best coding education resources.
                    </p>
                    <div class="text-sm text-primary-200">
                        © 2024 ArslanDevs. All rights reserved.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
