{"openapi": "3.0.4", "info": {"title": "CodingZoo API", "description": "API for CodingZoo - Your Ultimate Coding Education Platform", "version": "v1"}, "paths": {"/api/Articles": {"get": {"tags": ["Articles"], "parameters": [{"name": "Query", "in": "query", "schema": {"type": "string"}}, {"name": "CategoryId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Tags", "in": "query", "schema": {"type": "string"}}, {"name": "IsFeatured", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsPublished", "in": "query", "schema": {"type": "boolean"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortDirection", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ArticleListDtoPaginatedResultApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ArticleListDtoPaginatedResultApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ArticleListDtoPaginatedResultApiResponse"}}}}}}, "post": {"tags": ["Articles"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateArticleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateArticleDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateArticleDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ArticleDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ArticleDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ArticleDtoApiResponse"}}}}}}}, "/api/Articles/{id}": {"get": {"tags": ["Articles"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ArticleDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ArticleDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ArticleDtoApiResponse"}}}}}}, "put": {"tags": ["Articles"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateArticleDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateArticleDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateArticleDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ArticleDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ArticleDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ArticleDtoApiResponse"}}}}}}, "delete": {"tags": ["Articles"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Articles/slug/{slug}": {"get": {"tags": ["Articles"], "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ArticleDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ArticleDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ArticleDtoApiResponse"}}}}}}}, "/api/Articles/featured": {"get": {"tags": ["Articles"], "parameters": [{"name": "count", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 6}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ArticleListDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ArticleListDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ArticleListDtoListApiResponse"}}}}}}}, "/api/Articles/recent": {"get": {"tags": ["Articles"], "parameters": [{"name": "count", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ArticleListDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ArticleListDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ArticleListDtoListApiResponse"}}}}}}}, "/api/Articles/popular": {"get": {"tags": ["Articles"], "parameters": [{"name": "count", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ArticleListDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ArticleListDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ArticleListDtoListApiResponse"}}}}}}}, "/api/Articles/{id}/related": {"get": {"tags": ["Articles"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "count", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 5}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ArticleListDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ArticleListDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ArticleListDtoListApiResponse"}}}}}}}, "/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoginResponseDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDtoApiResponse"}}}}}}}, "/api/Auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDtoApiResponse"}}}}}}}, "/api/Auth/profile": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDtoApiResponse"}}}}}}, "put": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDtoApiResponse"}}}}}}}, "/api/Auth/change-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Auth/validate-token": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}}}}}}, "/api/Categories": {"get": {"tags": ["Categories"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CategoryDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CategoryDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CategoryDtoListApiResponse"}}}}}}, "post": {"tags": ["Categories"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCategoryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateCategoryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateCategoryDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CategoryDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CategoryDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CategoryDtoApiResponse"}}}}}}}, "/api/Categories/{id}": {"get": {"tags": ["Categories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CategoryDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CategoryDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CategoryDtoApiResponse"}}}}}}, "put": {"tags": ["Categories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCategoryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateCategoryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateCategoryDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CategoryDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CategoryDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CategoryDtoApiResponse"}}}}}}, "delete": {"tags": ["Categories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Categories/slug/{slug}": {"get": {"tags": ["Categories"], "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CategoryDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CategoryDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CategoryDtoApiResponse"}}}}}}}, "/api/Categories/{id}/articles": {"get": {"tags": ["Categories"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ArticleListDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ArticleListDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ArticleListDtoListApiResponse"}}}}}}}}, "components": {"schemas": {"ApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ArticleDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "title": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "summary": {"type": "string", "nullable": true}, "content": {"type": "string", "nullable": true}, "featuredImage": {"type": "string", "nullable": true}, "metaTitle": {"type": "string", "nullable": true}, "metaDescription": {"type": "string", "nullable": true}, "tags": {"type": "string", "nullable": true}, "readTimeMinutes": {"type": "integer", "format": "int32"}, "viewCount": {"type": "integer", "format": "int32"}, "isFeatured": {"type": "boolean"}, "isPublished": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time", "nullable": true}, "categoryId": {"type": "integer", "format": "int32"}, "authorId": {"type": "integer", "format": "int32"}, "category": {"$ref": "#/components/schemas/CategoryDto"}, "author": {"$ref": "#/components/schemas/UserDto"}, "tagList": {"type": "array", "items": {"type": "string"}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "ArticleDtoApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/ArticleDto"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ArticleListDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "title": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "summary": {"type": "string", "nullable": true}, "featuredImage": {"type": "string", "nullable": true}, "readTimeMinutes": {"type": "integer", "format": "int32"}, "viewCount": {"type": "integer", "format": "int32"}, "isFeatured": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "publishedAt": {"type": "string", "format": "date-time", "nullable": true}, "category": {"$ref": "#/components/schemas/CategoryDto"}, "author": {"$ref": "#/components/schemas/UserDto"}, "tagList": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ArticleListDtoListApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/ArticleListDto"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ArticleListDtoPaginatedResult": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/ArticleListDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}, "hasNextPage": {"type": "boolean", "readOnly": true}, "hasPreviousPage": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "ArticleListDtoPaginatedResultApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/ArticleListDtoPaginatedResult"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "BooleanApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "CategoryDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "color": {"type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "articleCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CategoryDtoApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/CategoryDto"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "CategoryDtoListApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDto"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "ChangePasswordDto": {"required": ["confirmPassword", "currentPassword", "newPassword"], "type": "object", "properties": {"currentPassword": {"minLength": 1, "type": "string"}, "newPassword": {"maxLength": 100, "minLength": 6, "type": "string"}, "confirmPassword": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "CreateArticleDto": {"required": ["categoryId", "content", "title"], "type": "object", "properties": {"title": {"maxLength": 200, "minLength": 0, "type": "string"}, "summary": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "content": {"minLength": 1, "type": "string"}, "featuredImage": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "metaTitle": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "metaDescription": {"maxLength": 300, "minLength": 0, "type": "string", "nullable": true}, "tags": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "readTimeMinutes": {"type": "integer", "format": "int32"}, "isFeatured": {"type": "boolean"}, "isPublished": {"type": "boolean"}, "categoryId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CreateCategoryDto": {"required": ["name"], "type": "object", "properties": {"name": {"maxLength": 100, "minLength": 0, "type": "string"}, "description": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "icon": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "color": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateUserDto": {"required": ["email", "firstName", "lastName", "password", "username"], "type": "object", "properties": {"firstName": {"maxLength": 100, "minLength": 0, "type": "string"}, "lastName": {"maxLength": 100, "minLength": 0, "type": "string"}, "email": {"maxLength": 200, "minLength": 0, "type": "string", "format": "email"}, "username": {"maxLength": 100, "minLength": 0, "type": "string"}, "password": {"maxLength": 100, "minLength": 6, "type": "string"}, "bio": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "website": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "twitterHandle": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "gitHubHandle": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "isAdmin": {"type": "boolean"}}, "additionalProperties": false}, "LoginDto": {"required": ["email", "password"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string", "format": "email"}, "password": {"minLength": 1, "type": "string"}, "rememberMe": {"type": "boolean"}}, "additionalProperties": false}, "LoginResponseDto": {"type": "object", "properties": {"token": {"type": "string", "nullable": true}, "user": {"$ref": "#/components/schemas/UserDto"}, "expiresAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "LoginResponseDtoApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/LoginResponseDto"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UpdateArticleDto": {"required": ["categoryId", "content", "title"], "type": "object", "properties": {"title": {"maxLength": 200, "minLength": 0, "type": "string"}, "summary": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "content": {"minLength": 1, "type": "string"}, "featuredImage": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "metaTitle": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "metaDescription": {"maxLength": 300, "minLength": 0, "type": "string", "nullable": true}, "tags": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "readTimeMinutes": {"type": "integer", "format": "int32"}, "isFeatured": {"type": "boolean"}, "isPublished": {"type": "boolean"}, "categoryId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "UpdateCategoryDto": {"required": ["name"], "type": "object", "properties": {"name": {"maxLength": 100, "minLength": 0, "type": "string"}, "description": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "icon": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "color": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UpdateUserDto": {"required": ["email", "firstName", "lastName", "username"], "type": "object", "properties": {"firstName": {"maxLength": 100, "minLength": 0, "type": "string"}, "lastName": {"maxLength": 100, "minLength": 0, "type": "string"}, "email": {"maxLength": 200, "minLength": 0, "type": "string", "format": "email"}, "username": {"maxLength": 100, "minLength": 0, "type": "string"}, "avatar": {"maxLength": 200, "minLength": 0, "type": "string", "nullable": true}, "bio": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "website": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "twitterHandle": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "gitHubHandle": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "isAdmin": {"type": "boolean"}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UserDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "username": {"type": "string", "nullable": true}, "avatar": {"type": "string", "nullable": true}, "bio": {"type": "string", "nullable": true}, "website": {"type": "string", "nullable": true}, "twitterHandle": {"type": "string", "nullable": true}, "gitHubHandle": {"type": "string", "nullable": true}, "isAdmin": {"type": "boolean"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "lastLoginAt": {"type": "string", "format": "date-time", "nullable": true}, "fullName": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "UserDtoApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/UserDto"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Enter 'Bearer' [space] and then your token in the text input below.", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}