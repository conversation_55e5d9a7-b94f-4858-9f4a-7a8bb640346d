using CodingZoo.Shared.Models;

namespace CodingZoo.API.Data;

public static class SampleDataSeeder
{
    public static async Task SeedSampleDataAsync(CodingZooDbContext context)
    {
        // Check if data already exists
        if (context.Articles.Any())
        {
            return; // Data already seeded
        }

        // Get the admin user
        var adminUser = context.Users.First(u => u.Email == "<EMAIL>");

        // Sample articles
        var sampleArticles = new List<Article>
        {
            new Article
            {
                Title = "Getting Started with C# Programming",
                Slug = "getting-started-with-csharp-programming",
                Summary = "Learn the fundamentals of C# programming language, from basic syntax to object-oriented concepts.",
                Content = @"# Getting Started with C# Programming

C# is a modern, object-oriented programming language developed by Microsoft. It's part of the .NET ecosystem and is widely used for building various types of applications.

## What is C#?

C# (pronounced ""C-sharp"") is a general-purpose programming language that combines the power of C++ with the simplicity of Visual Basic. It was designed to be simple, modern, and powerful.

## Key Features

- **Object-Oriented**: Everything in C# is an object
- **Type-Safe**: Prevents common programming errors
- **Memory Management**: Automatic garbage collection
- **Cross-Platform**: Runs on Windows, macOS, and Linux

## Your First C# Program

```csharp
using System;

class Program
{
    static void Main()
    {
        Console.WriteLine(""Hello, CodingZoo!"");
    }
}
```

This simple program demonstrates the basic structure of a C# application.",
                FeaturedImage = "https://via.placeholder.com/800x400/239120/FFFFFF?text=C%23",
                MetaTitle = "Getting Started with C# Programming - CodingZoo",
                MetaDescription = "Learn C# programming from scratch with our comprehensive beginner's guide. Master the fundamentals and start building applications today.",
                Tags = "csharp,programming,beginner,dotnet,tutorial",
                ReadTimeMinutes = 8,
                ViewCount = 1250,
                IsFeatured = true,
                IsPublished = true,
                CategoryId = 1,
                AuthorId = adminUser.Id,
                CreatedAt = DateTime.UtcNow.AddDays(-30),
                UpdatedAt = DateTime.UtcNow.AddDays(-30),
                PublishedAt = DateTime.UtcNow.AddDays(-30)
            },
            new Article
            {
                Title = "Python for Beginners: Complete Guide",
                Slug = "python-for-beginners-complete-guide",
                Summary = "Master Python programming with this comprehensive guide covering syntax, data structures, and practical examples.",
                Content = @"# Python for Beginners: Complete Guide

Python is one of the most popular programming languages in the world, known for its simplicity and readability.

## Why Choose Python?

- **Easy to Learn**: Simple, readable syntax
- **Versatile**: Web development, data science, AI, automation
- **Large Community**: Extensive libraries and frameworks
- **High Demand**: Popular in job market

## Python Basics

### Variables and Data Types

```python
# Variables
name = ""CodingZoo""
age = 25
is_learning = True

# Data types
numbers = [1, 2, 3, 4, 5]
person = {""name"": ""John"", ""age"": 30}
```

### Functions

```python
def greet(name):
    return f""Hello, {name}!""

message = greet(""CodingZoo"")
print(message)
```

Start your Python journey today!",
                FeaturedImage = "https://via.placeholder.com/800x400/3776AB/FFFFFF?text=Python",
                MetaTitle = "Python for Beginners: Complete Programming Guide - CodingZoo",
                MetaDescription = "Learn Python programming from basics to advanced concepts. Perfect for beginners with hands-on examples and practical projects.",
                Tags = "python,programming,beginner,tutorial,guide",
                ReadTimeMinutes = 12,
                ViewCount = 2100,
                IsFeatured = true,
                IsPublished = true,
                CategoryId = 2,
                AuthorId = adminUser.Id,
                CreatedAt = DateTime.UtcNow.AddDays(-25),
                UpdatedAt = DateTime.UtcNow.AddDays(-25),
                PublishedAt = DateTime.UtcNow.AddDays(-25)
            },
            new Article
            {
                Title = "Modern JavaScript ES6+ Features",
                Slug = "modern-javascript-es6-features",
                Summary = "Explore the latest JavaScript features including arrow functions, destructuring, async/await, and more.",
                Content = @"# Modern JavaScript ES6+ Features

JavaScript has evolved significantly with ES6 and later versions, introducing powerful new features that make code more readable and efficient.

## Arrow Functions

```javascript
// Traditional function
function add(a, b) {
    return a + b;
}

// Arrow function
const add = (a, b) => a + b;
```

## Destructuring

```javascript
// Array destructuring
const [first, second] = [1, 2, 3];

// Object destructuring
const {name, age} = {name: 'John', age: 30};
```

## Template Literals

```javascript
const name = 'CodingZoo';
const message = `Welcome to ${name}!`;
```

## Async/Await

```javascript
async function fetchData() {
    try {
        const response = await fetch('/api/data');
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error:', error);
    }
}
```

These features make JavaScript more powerful and enjoyable to work with!",
                FeaturedImage = "https://via.placeholder.com/800x400/F7DF1E/000000?text=JavaScript",
                MetaTitle = "Modern JavaScript ES6+ Features Guide - CodingZoo",
                MetaDescription = "Master modern JavaScript with ES6+ features. Learn arrow functions, destructuring, async/await, and more with practical examples.",
                Tags = "javascript,es6,modern,web-development,frontend",
                ReadTimeMinutes = 10,
                ViewCount = 1800,
                IsFeatured = true,
                IsPublished = true,
                CategoryId = 3,
                AuthorId = adminUser.Id,
                CreatedAt = DateTime.UtcNow.AddDays(-20),
                UpdatedAt = DateTime.UtcNow.AddDays(-20),
                PublishedAt = DateTime.UtcNow.AddDays(-20)
            },
            new Article
            {
                Title = "Building Your First React Application",
                Slug = "building-your-first-react-application",
                Summary = "Learn how to create a React application from scratch, including components, state management, and best practices.",
                Content = @"# Building Your First React Application

React is a popular JavaScript library for building user interfaces, especially single-page applications where you need a fast, interactive user experience.

## What is React?

React is a declarative, efficient, and flexible JavaScript library for building user interfaces. It lets you compose complex UIs from small and isolated pieces of code called ""components.""

## Creating Your First Component

```jsx
import React from 'react';

function Welcome(props) {
    return <h1>Hello, {props.name}!</h1>;
}

export default Welcome;
```

## Using State with Hooks

```jsx
import React, { useState } from 'react';

function Counter() {
    const [count, setCount] = useState(0);

    return (
        <div>
            <p>You clicked {count} times</p>
            <button onClick={() => setCount(count + 1)}>
                Click me
            </button>
        </div>
    );
}
```

## Props and Component Communication

```jsx
function App() {
    return (
        <div>
            <Welcome name=""CodingZoo"" />
            <Counter />
        </div>
    );
}
```

React makes building interactive UIs straightforward and enjoyable!",
                FeaturedImage = "https://via.placeholder.com/800x400/61DAFB/000000?text=React",
                MetaTitle = "Building Your First React Application - CodingZoo",
                MetaDescription = "Learn React from scratch with this comprehensive guide. Build your first React app with components, hooks, and best practices.",
                Tags = "react,javascript,frontend,components,hooks",
                ReadTimeMinutes = 15,
                ViewCount = 2500,
                IsFeatured = true,
                IsPublished = true,
                CategoryId = 4,
                AuthorId = adminUser.Id,
                CreatedAt = DateTime.UtcNow.AddDays(-15),
                UpdatedAt = DateTime.UtcNow.AddDays(-15),
                PublishedAt = DateTime.UtcNow.AddDays(-15)
            },
            new Article
            {
                Title = "Node.js Backend Development Fundamentals",
                Slug = "nodejs-backend-development-fundamentals",
                Summary = "Master backend development with Node.js, including Express.js, middleware, routing, and database integration.",
                Content = @"# Node.js Backend Development Fundamentals

Node.js is a powerful runtime environment that allows you to run JavaScript on the server side, making it possible to build scalable backend applications.

## What is Node.js?

Node.js is built on Chrome's V8 JavaScript engine and uses an event-driven, non-blocking I/O model that makes it lightweight and efficient.

## Setting Up Express.js

```javascript
const express = require('express');
const app = express();
const port = 3000;

app.use(express.json());

app.get('/', (req, res) => {
    res.json({ message: 'Welcome to CodingZoo API!' });
});

app.listen(port, () => {
    console.log(`Server running at http://localhost:${port}`);
});
```

## Creating API Routes

```javascript
// GET route
app.get('/api/users', (req, res) => {
    res.json({ users: [] });
});

// POST route
app.post('/api/users', (req, res) => {
    const { name, email } = req.body;
    // Save user logic here
    res.status(201).json({ message: 'User created successfully' });
});
```

## Middleware

```javascript
// Logging middleware
app.use((req, res, next) => {
    console.log(`${req.method} ${req.path} - ${new Date().toISOString()}`);
    next();
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ error: 'Something went wrong!' });
});
```

Node.js opens up endless possibilities for backend development!",
                FeaturedImage = "https://via.placeholder.com/800x400/339933/FFFFFF?text=Node.js",
                MetaTitle = "Node.js Backend Development Fundamentals - CodingZoo",
                MetaDescription = "Learn Node.js backend development with Express.js, middleware, routing, and API creation. Perfect for JavaScript developers.",
                Tags = "nodejs,backend,express,javascript,api",
                ReadTimeMinutes = 18,
                ViewCount = 1900,
                IsFeatured = true,
                IsPublished = true,
                CategoryId = 5,
                AuthorId = adminUser.Id,
                CreatedAt = DateTime.UtcNow.AddDays(-10),
                UpdatedAt = DateTime.UtcNow.AddDays(-10),
                PublishedAt = DateTime.UtcNow.AddDays(-10)
            },
            new Article
            {
                Title = "Java Programming: Object-Oriented Concepts",
                Slug = "java-programming-object-oriented-concepts",
                Summary = "Understand the core principles of object-oriented programming in Java including classes, inheritance, and polymorphism.",
                Content = @"# Java Programming: Object-Oriented Concepts

Java is a robust, object-oriented programming language that follows the principle of ""write once, run anywhere"" (WORA).

## Classes and Objects

```java
public class Car {
    private String brand;
    private String model;
    private int year;
    
    public Car(String brand, String model, int year) {
        this.brand = brand;
        this.model = model;
        this.year = year;
    }
    
    public void start() {
        System.out.println(""The "" + brand + "" "" + model + "" is starting..."");
    }
}

// Creating an object
Car myCar = new Car(""Toyota"", ""Camry"", 2023);
myCar.start();
```

## Inheritance

```java
public class ElectricCar extends Car {
    private int batteryCapacity;
    
    public ElectricCar(String brand, String model, int year, int batteryCapacity) {
        super(brand, model, year);
        this.batteryCapacity = batteryCapacity;
    }
    
    public void charge() {
        System.out.println(""Charging the electric car..."");
    }
}
```

## Polymorphism

```java
public interface Vehicle {
    void start();
    void stop();
}

public class Car implements Vehicle {
    public void start() {
        System.out.println(""Car is starting..."");
    }
    
    public void stop() {
        System.out.println(""Car is stopping..."");
    }
}
```

Java's OOP principles make code more organized, reusable, and maintainable!",
                FeaturedImage = "https://via.placeholder.com/800x400/ED8B00/FFFFFF?text=Java",
                MetaTitle = "Java Programming: Object-Oriented Concepts - CodingZoo",
                MetaDescription = "Master Java OOP concepts including classes, objects, inheritance, and polymorphism with practical examples and best practices.",
                Tags = "java,oop,programming,classes,inheritance",
                ReadTimeMinutes = 14,
                ViewCount = 1600,
                IsFeatured = true,
                IsPublished = true,
                CategoryId = 6,
                AuthorId = adminUser.Id,
                CreatedAt = DateTime.UtcNow.AddDays(-5),
                UpdatedAt = DateTime.UtcNow.AddDays(-5),
                PublishedAt = DateTime.UtcNow.AddDays(-5)
            }
        };

        context.Articles.AddRange(sampleArticles);
        await context.SaveChangesAsync();
    }
}
