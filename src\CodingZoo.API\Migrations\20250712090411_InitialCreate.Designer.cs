﻿// <auto-generated />
using System;
using CodingZoo.API.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace CodingZoo.API.Migrations
{
    [DbContext(typeof(CodingZooDbContext))]
    [Migration("20250712090411_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.7")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("CodingZoo.Shared.Models.Article", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AuthorId")
                        .HasColumnType("int");

                    b.Property<int>("CategoryId")
                        .HasColumnType("int");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("FeaturedImage")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("IsFeatured")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPublished")
                        .HasColumnType("bit");

                    b.Property<string>("MetaDescription")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<string>("MetaTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("PublishedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("ReadTimeMinutes")
                        .HasColumnType("int");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<string>("Summary")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Tags")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("ViewCount")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AuthorId");

                    b.HasIndex("CategoryId");

                    b.HasIndex("Slug")
                        .IsUnique();

                    b.ToTable("Articles");
                });

            modelBuilder.Entity("CodingZoo.Shared.Models.ArticleBookmark", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ArticleId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.HasIndex("ArticleId", "UserId")
                        .IsUnique();

                    b.ToTable("ArticleBookmarks");
                });

            modelBuilder.Entity("CodingZoo.Shared.Models.Category", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Color")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Icon")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Slug")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("Slug")
                        .IsUnique();

                    b.ToTable("Categories");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Color = "#239120",
                            CreatedAt = new DateTime(2025, 7, 12, 9, 4, 9, 412, DateTimeKind.Utc).AddTicks(5861),
                            Description = "C# programming language tutorials and guides",
                            Icon = "devicon-csharp-plain",
                            IsActive = true,
                            Name = "C#",
                            Slug = "csharp",
                            SortOrder = 1,
                            UpdatedAt = new DateTime(2025, 7, 12, 9, 4, 9, 412, DateTimeKind.Utc).AddTicks(5863)
                        },
                        new
                        {
                            Id = 2,
                            Color = "#3776AB",
                            CreatedAt = new DateTime(2025, 7, 12, 9, 4, 9, 412, DateTimeKind.Utc).AddTicks(7574),
                            Description = "Python programming tutorials and examples",
                            Icon = "devicon-python-plain",
                            IsActive = true,
                            Name = "Python",
                            Slug = "python",
                            SortOrder = 2,
                            UpdatedAt = new DateTime(2025, 7, 12, 9, 4, 9, 412, DateTimeKind.Utc).AddTicks(7575)
                        },
                        new
                        {
                            Id = 3,
                            Color = "#F7DF1E",
                            CreatedAt = new DateTime(2025, 7, 12, 9, 4, 9, 412, DateTimeKind.Utc).AddTicks(7579),
                            Description = "JavaScript tutorials and modern web development",
                            Icon = "devicon-javascript-plain",
                            IsActive = true,
                            Name = "JavaScript",
                            Slug = "javascript",
                            SortOrder = 3,
                            UpdatedAt = new DateTime(2025, 7, 12, 9, 4, 9, 412, DateTimeKind.Utc).AddTicks(7580)
                        },
                        new
                        {
                            Id = 4,
                            Color = "#61DAFB",
                            CreatedAt = new DateTime(2025, 7, 12, 9, 4, 9, 412, DateTimeKind.Utc).AddTicks(7581),
                            Description = "React.js tutorials and component development",
                            Icon = "devicon-react-original",
                            IsActive = true,
                            Name = "React",
                            Slug = "react",
                            SortOrder = 4,
                            UpdatedAt = new DateTime(2025, 7, 12, 9, 4, 9, 412, DateTimeKind.Utc).AddTicks(7582)
                        },
                        new
                        {
                            Id = 5,
                            Color = "#339933",
                            CreatedAt = new DateTime(2025, 7, 12, 9, 4, 9, 412, DateTimeKind.Utc).AddTicks(7583),
                            Description = "Node.js backend development tutorials",
                            Icon = "devicon-nodejs-plain",
                            IsActive = true,
                            Name = "Node.js",
                            Slug = "nodejs",
                            SortOrder = 5,
                            UpdatedAt = new DateTime(2025, 7, 12, 9, 4, 9, 412, DateTimeKind.Utc).AddTicks(7583)
                        },
                        new
                        {
                            Id = 6,
                            Color = "#ED8B00",
                            CreatedAt = new DateTime(2025, 7, 12, 9, 4, 9, 412, DateTimeKind.Utc).AddTicks(7585),
                            Description = "Java programming language tutorials",
                            Icon = "devicon-java-plain",
                            IsActive = true,
                            Name = "Java",
                            Slug = "java",
                            SortOrder = 6,
                            UpdatedAt = new DateTime(2025, 7, 12, 9, 4, 9, 412, DateTimeKind.Utc).AddTicks(7585)
                        });
                });

            modelBuilder.Entity("CodingZoo.Shared.Models.Comment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ArticleId")
                        .HasColumnType("int");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("bit");

                    b.Property<int?>("ParentCommentId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ArticleId");

                    b.HasIndex("ParentCommentId");

                    b.HasIndex("UserId");

                    b.ToTable("Comments");
                });

            modelBuilder.Entity("CodingZoo.Shared.Models.Media", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AltText")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<string>("OriginalFileName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("UploadedByUserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UploadedByUserId");

                    b.ToTable("Media");
                });

            modelBuilder.Entity("CodingZoo.Shared.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Avatar")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Bio")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("GitHubHandle")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsAdmin")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TwitterHandle")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Website")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.HasIndex("Username")
                        .IsUnique();

                    b.ToTable("Users");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Avatar = "",
                            Bio = "Administrator of CodingZoo - Your Ultimate Coding Education Platform",
                            CreatedAt = new DateTime(2025, 7, 12, 9, 4, 9, 623, DateTimeKind.Utc).AddTicks(172),
                            Email = "<EMAIL>",
                            FirstName = "Admin",
                            GitHubHandle = "",
                            IsActive = true,
                            IsAdmin = true,
                            LastName = "User",
                            PasswordHash = "$2a$11$abbu6s7ftsFAR/i3.v4RbetbtEitSlnsCO0RrT133oigjUSiVCdO.",
                            TwitterHandle = "",
                            UpdatedAt = new DateTime(2025, 7, 12, 9, 4, 9, 623, DateTimeKind.Utc).AddTicks(379),
                            Username = "admin",
                            Website = ""
                        });
                });

            modelBuilder.Entity("CodingZoo.Shared.Models.Article", b =>
                {
                    b.HasOne("CodingZoo.Shared.Models.User", "Author")
                        .WithMany("Articles")
                        .HasForeignKey("AuthorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("CodingZoo.Shared.Models.Category", "Category")
                        .WithMany("Articles")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Author");

                    b.Navigation("Category");
                });

            modelBuilder.Entity("CodingZoo.Shared.Models.ArticleBookmark", b =>
                {
                    b.HasOne("CodingZoo.Shared.Models.Article", "Article")
                        .WithMany("Bookmarks")
                        .HasForeignKey("ArticleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CodingZoo.Shared.Models.User", "User")
                        .WithMany("Bookmarks")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Article");

                    b.Navigation("User");
                });

            modelBuilder.Entity("CodingZoo.Shared.Models.Comment", b =>
                {
                    b.HasOne("CodingZoo.Shared.Models.Article", "Article")
                        .WithMany("Comments")
                        .HasForeignKey("ArticleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("CodingZoo.Shared.Models.Comment", "ParentComment")
                        .WithMany("Replies")
                        .HasForeignKey("ParentCommentId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("CodingZoo.Shared.Models.User", "User")
                        .WithMany("Comments")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Article");

                    b.Navigation("ParentComment");

                    b.Navigation("User");
                });

            modelBuilder.Entity("CodingZoo.Shared.Models.Media", b =>
                {
                    b.HasOne("CodingZoo.Shared.Models.User", "UploadedBy")
                        .WithMany()
                        .HasForeignKey("UploadedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("UploadedBy");
                });

            modelBuilder.Entity("CodingZoo.Shared.Models.Article", b =>
                {
                    b.Navigation("Bookmarks");

                    b.Navigation("Comments");
                });

            modelBuilder.Entity("CodingZoo.Shared.Models.Category", b =>
                {
                    b.Navigation("Articles");
                });

            modelBuilder.Entity("CodingZoo.Shared.Models.Comment", b =>
                {
                    b.Navigation("Replies");
                });

            modelBuilder.Entity("CodingZoo.Shared.Models.User", b =>
                {
                    b.Navigation("Articles");

                    b.Navigation("Bookmarks");

                    b.Navigation("Comments");
                });
#pragma warning restore 612, 618
        }
    }
}
