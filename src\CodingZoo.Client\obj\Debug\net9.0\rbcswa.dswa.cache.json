{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["Z78u9zy1pTw8ztVFe4hp6HpVzCggcYmikTqUvZhmXJ0=", "KD6wyboCIrVgSKIgTvRN6Pjuyki8bdbDHkXAxT7YKuI=", "RGCgdjXA7aKnxo5+MhKNZZqUkNjSg5eVFOK2QdNsvv4=", "n7SnjHxYDA28JXWLWFwbmPkCL/rNiWj3swcW490YzZw=", "znM0UvfpjNz0pRjUuPeXIr7Akyc0Xe9P4Jv5A0QrgEk=", "wkgu6rfV3NAo3hGuqhap5EgUK2XNtX3rF34ZNBdA89Q=", "+8T3HzreDft4NllGnPOZde5GKZLd8bDEAsj3QwUZjPs=", "xj0nGNOpoo343WS+rryfLy3xlhtNAklPw9K73xC9wcU=", "MPBZSoEaYEIkjrr5fvBoSc/NW2wjAC8oaggG7N5BshQ=", "xrLkv4Fhy0K2V89nYXXi8oZg8y7uECgLbr9r+tsqP7E=", "FqPmJ1n6KrXMYUOYumYfLB1pW3xFpZ1LXxCvfGZWM04=", "qZMqO7TEL9uE7Oeowj0O80S9aLR8Thi53IFZjF/Emag=", "itA3UW/SweusSwZ7YJ7zqsYvpEoA2EC2lrW1D3ooI9E=", "sxkLYYi39X5LSBlg5F2s6zfaEBjilfPox1bUiNC30fc=", "R5lDCV2uOnw0dOa+KBCYgO/nu1sF1MVBVVgGcQ+FsSc=", "+NwBHtJURLsaFU5mpLGmxXYUSsBJotcd/wjiEe6ZipE=", "Jjv+fxKXACdO6NOde4K5UlZ0EzW74hgFtutuA1Vynu0=", "XCcd7vSNNyh3OpIvGPsr79bZD3RBRMPkulcJqlxXI3c=", "tmbyZf70q+TtKgltS5Hght13Pe5Rl3MQL0dEfijqfTY=", "8f0qa0OxBVMtAma16Otd/yf21+BQUqcoCcc+YFM8K+I=", "emAMrZTU8A2dL0ADHI7PZZkG40eCn3LmK7mHtlbLCb8=", "K47+Cjz+DsCXAIr021D8aQw2VXIThWkX2EtWj2zbjVo=", "X6eJTl0mjZtjlPhK44v60TdQq2vffHEgfMNuCzIBZhc=", "hvlHlTemtTB/wkGLeBTLwRTc44i1cn+mgTTCDgWc6Mw=", "6JEXMd5Roo/0+4N+dnv69uIkQdq8Q4LmbdCdqtS74cA=", "LVaIr6voax6cj6dv+M/EgCYgqSdVDxp1x2nUVUSv+ng=", "uvLpcz3FcpPKOL+CHfJVTr6sAQh8wdF+p9Ab6G0Lhn4=", "Bukiye8rVR+zSu3Aj3mpefC9xS3QNpjF98T9XlQvpmA=", "ed4OzkiFAgqyxn/Qvf6MFIxWLTOjl06gO8TisTGOoTw=", "zoMzEbPiiC8l7XgV1C7mX6BU/H1HVunNl5cJRdD8qWA=", "9ByGx0jsYtFEykcSXG4igJZ1EEr5lsbM1WMnNLQ4loM=", "pc9wdcARo8YhY1Su5aF9/Fy1qaVxvnTcoWatsXWgeQc=", "j3BwkK+WA/cL+8L6RYhtXntdGkdK3VBVUHAcuWB+Zvk=", "OXO6XG8j3iFP2Sz1FBGFSctsy6SG7qAEBjiz7VHZq9s=", "fR2Ym5wBZZrKtTrFZhooQ8Ei8rloxiTV3gO6W+TmxtY=", "RmIdX9aNQbQ91WTZvAMJ1uV4S4fH0BkQIQBQXwI+SPY=", "XS7NLPzzziwafpf1kqAdLwjLDU52EcD6Es0LEiTfG7A=", "5iglABheIom+jYUKvmMLmd79sodcXTPOUoCWA6hw3vU=", "a2lu/yTQzXQq4DkIWsSVEa+FnLicvdOQm9HrQNrT3BU=", "QYRmaVF3jvZ8GeodIPGdN2h36XfExUIeIDjsGxwyMc8=", "Ffj3pTFlL7PrhnYYcVzT5RBIjOYuZr7jOS8FsF4Xb9Q=", "b1726SCDbya2m4Y9KNoXjw6Zld3A+W4B5cI4XBroYMw=", "rbQok7MMzbnEQx6hMzo/LJN4KY/x0RlbO5AYTrRz6G4=", "GPAZ0qPBpQU1/yQzXM6x9qU9vQqcfSEboecqhYBbHJ0=", "yVD9fpc7V0h3G5Fshzk88JiXl6kaalocOapuzFeNQJg=", "STCy/Z5+oYKNU45J1jSzmqx8hbvW5wJ2u/zN2GduY30=", "meFhxgQ7dxerq/CuoTkjA1SHD8z/5MM3B+4ZCCOhRVQ=", "RvXAQAaqKxoMcdx2aucT9ecp3IGevs9u48lfbPAJlZA=", "C8nkN1czbPUk4/2Oyg01pFPg6HwaljY+7QvfAZz2v6Q=", "OSXkAbTVORhu3DVbUbacQe4BCASOkRu0HjmOR7GgqDc=", "hHG2lrSJYPrnHzBccRKfWpqGkWuyoEdIazQDiYqnfG8=", "2vr/luuFlBZDIIgTV7xlfbZS8HZcWNpA2HuGqwgVko4=", "OnDptIUvOvKLJaVHxm9dXqY1MWU0NVyBkVvTq26F71k=", "gghhNLJFbTwU/z/ltkJFK77MXbqAKQa2GJAmyHFIy28=", "ozMFf1Crrf8sbf59OK++7gofRFUV7mF2TrHy8Mo8gEM=", "fSSt1mSrx9nWZdMdU9l8E65BgohgDkbERAsZGVn4tc8=", "yN1UnvS5KQc1/GiIQQ5/5nwU+R3+ikLqv3XJ7qMo80Y=", "8I4IJ2EmsTJP0f7ZDagMwbhgLZkBQvZg61edgpYppzk=", "Z+9xzWiLqL3APgIP1gdAt2GyqA8OgaZEeml1H1r3vYo=", "q79R5ch3hYXyI9jdozM80u0fwP16TD8WoOCM234L97k=", "1yXYfPTaD7kmkCTyDhjl/8qqyPduXvR/Fz22KOqsEqQ=", "anJq09GkslIjA4wGT8H+OfnUV2oXlT/vFHDJyb2lL8M=", "DnYZonfMxdmXQMH7qcx9BEExjGHsb65tjIaCy7H2tYk=", "E8KqCno++xi98V4CRg3bphJvA79+B/iA7vB9ZgKMJ1c=", "Vx7W7ubrx1e+aVKArTQN7xbF1HF8XQLjF1Wui8/0R8o=", "zhJx6zBD3vovetbyL/DvKgLpOyDw+9omlJeO+ci4KT4=", "5LD1R6Of78FIbnpMJp2BGRABbzfQQ8skgJDL/NOAeC0=", "0c05DMMJTJMPkw+TT0vcB9SrLDc5QHedar/JZE0SmAs=", "iS6126o6RpIWhLhd9AcmlSbPrkMARUO401Qr2pJ0QdM=", "bNN4BEC1yuw+0xitLaGx19nA6mRExuOAGiv1e4xIGhw=", "yd+hZ+tDE4BPJ6NRpbQmPBCJkJyvdsF0UIU8dvX8xAY=", "C2w2rPJP7WHuZs2l7dMcSll1chrxGucdKDLUpFY6bng=", "+9N1MaIRKVsP5ZXBpooIfccgmL+6ZO+XlATcq4o0i6g=", "kjP84EiOqxqZFu4ciifbcRbO8LQ62IQYdmy9OJtCLU0=", "JmIVKymzK15iV069onIY352QLJfpcCKcX7LKcNTfyMo=", "LuSTMZoSkCwf0ltxw/7PDLZ2BS7L1te09L6zQ4Kshxw=", "qVEu22/Dp0YdRF1zsnkU275aQKvKWZpfjC2ukJEhQQ0=", "Pc5Zqp5wx2SJ3DyYnzLrUmR7l/K+C4EHHXm9v+ilrG4=", "DMMf0Oz/N2wvaDsvogxVLayYz+NPB5vDEWRxJQr+uUI=", "V6QBwrSxOJJjuinJzkeyIaGSRjt7c/fzINTYyxRrQCg=", "Fh6Zg1Zvi9o+uQq5YipIIhrDT+XQBDDlpRO+8T/vWtI=", "0X4ZZJNLFQvxadw4UbMV1ZaXqlX/OxI4E/152A4n/Vg=", "ecTMtFA75e1va40do+R2Q6rbrvxa5hbS9vWUpZ9axbw=", "E5BwUFTodYSea/vbCAwUQY98u2jTj3IznWn8f3UcAu0=", "a9TGqZU7cn63W2Jk/nzpLGhiA3q6mQo1B9EZq5u4GhU=", "qBXv5+vtMmPk0zn2X8wZZ1ncDg3r6znfyn1Ldu/qE0c=", "7Bi5T+EM9oODWNYl2G/zbl+u8b//c9qKCue3mxezrE0=", "lrfn2Ib3mYqQGNx4i5LqlA3EYN3rPbGJPfz2GusLM70=", "6uYScJL6xn8SjemrTqOJ7MXm/G6qpqWDNCtewfzgqrU=", "Zt60htbiXFG0GVtQxk1xR5NItf/2CE2B9wENt4WBQMk=", "cIGDTN0Uc+pdChWfeeAxLORKPr8gqWLtGzqvfWSQds8=", "EUMHUjAtPzLqC/t3sj0S7RDDfrg2/g4sptNZEa3fkT8=", "nn9neqZdf3HFkMivfGHCJf1OlEC8oNpk/ykNS+9ZN5o=", "sIfblPs8PIgX7DL2DBCc5g4NYup+W1HjFzKlTLp66xc=", "YlSFttV5vNVdAWZGmBF93wUvL0tm0V/VCuGLHPUAdoQ=", "4bVpRAi2+X2rNP6rxtKecMMqICRHnxNBDnsGhv89aKw=", "DYInXy9zcem73OpkYUdUR91rEKSAeqGjH4p0r9YC+g0=", "8md1dTlSy4z6i9GTY0oQ9B/Uk7uaXiMbnZhVuogNmKY=", "gEET6LbgZXPQgwcWcXGqH/2aSqUVPAz61FUsLFWVN58=", "neGxEgrcHK1cHOJL+pyKdikE7y9+WytUORl/d6xfpGE=", "6r3nAv/0OihGBkWmmg2Hn6hJa74aNxFtSfYklPjnyrw=", "Weg202yGrU/hr5LJJ2EKmTU3urytxqtdxa4F1FBaSRE=", "VVSs8QLplodxxAqC3zzeeSoB0CknUYnoKDvgwyictXw=", "JF/5vfxoy2IpEJdy+7AVDvQDguZkLsk08zPEcX5nnvs=", "hvRYg/ws98fxNbApYccCUulfOP2eTLdxnhbTzLALSt8=", "m0UXmhMn7MR18YErthieBQvsbQKi1Um0z/4ObJZk9AA=", "P8JH6eyeBjawkDMmtpV0CQ8PwyuaIylBFkxsmHyEtAw=", "fbms1AY9xIZHbhEyn0YjOUS5WkhGgCfpE3ujVk7oLj8=", "4gtTfR/oHy1o4JD+kbmg8NOixu8g788gf5vOCcMUAJE=", "wMOWg1v201bjHkhjc5U3NzLZ6RzA3PfGKapkwqMghg4=", "fc6Gwd63Vnf2GirrdRwHC7euxVUSxDMt5J7aOFe7w/s=", "zmeFArWjWYk8rbyMRbX7vJY7mrnsU3Jn0R5Gf8NhHT8=", "Sb5KZwlYGJra5U+OdYioQOy7kQt11nxke6k2nKA5vQc=", "+SoI7/FQ0D8dg3uCPS+JPWiVA2MmMaH9WkTWX+4KRr0=", "8dwPz8bs48HoF3GCZqJvSnI3azJr7IpfmLDMd600ZP0=", "9azLLexF5QCUhQUebRVvmQ7yStRNPQ5DNTCQzAkrBJA=", "ODpYHrEsYpnFzmQw0x+slOWAEp7mFf5j82Fnc41VglA=", "VpbpPoK7tIPA2zhvAX3I723TZnBhTx5IywNG9aY1J5E=", "uo8DA0IAKVEZrRYIoM2ld3UuLdP4oyg1heOzb2VvnZg=", "MeJpUq/OiG8LURYDoB1Xwm1Z7EXXe3ZwPqfQK+XGLxU=", "5zD+pDpNyeH2nkrMMyL8Rag60tUNtcDPubN7pAbnXD8=", "r8KOmINgQn2vSnh+NZSwdhr2A44KfgaKEltHC50vosY=", "iQl3U/Nt5q6cy7YercP4k9p3DXI7yO4Dt+mJIc5GAN8=", "Gl/HUnMSLJKcMAgX9A2mBsRLceD1/OkrujGvaUalVg0=", "9hEpr8Ffu7X6Npsk9S2B7cg8sTONxIHz55cpct0+/UE=", "elS+cCoc2PwiCkOnY4KltLUHN5rOhXAJWYpKYAD7Nzw=", "Dt9wgtInuttrMMTcHBSm2Bk2NLUuHhGzjf0FUr/DCmM=", "42ck61dpCzPoBpI3HPhb0TsYzZJcKJgMDxvcc9BiyfU=", "+5x8Ms1Ez3dGxn/SbK4VMqefRCWEk6C6ajHNoACVF/4=", "8z9QRxLK07/DMATdc7gFpJHU3V5x+syXoK0A6tUUs5o=", "zFeZRCQThsHIl12FHSKRrknGQ+2b0Bk+r0CkZ0c84qs=", "/JEQkkddLeXERCU/fspHaHEbX21CREPEL3QbJJ+/ULU=", "dx0eKLBTTjZ+CV/19vMRa+IYn6vCW5z+zBVx7JtZ68c=", "9UxGsSybeYzuLLPkzts9W9aqSa7ss/j0ZQOOdGw+XMk=", "LKrVmOTSv9te+7XKV7At9UAqhF1f9VMH54N0zDsmJQE=", "hcTUDHiFtrS9yGAbt7u9NQ1olN+LfVhlfTavoVHVJFg=", "C4Cvlwq2S4zcMN7y/rassPV2ynp5AwlT3qBRBKBemH4=", "g/HwzjmxsSBDuUDkyYPmBM/o/9fTIMyB/QasIoCJ9/A=", "D/i5ZomvLM4p73WqwoJL1Mh4Y6Qe7f2W5afyK51F0Ww=", "PYGxjaii9eriGDpddBE4JSITSz6TB1cc7GjFlTn+fWg=", "zsjKSVWY7EDMAQCqSDbhYfCs19aV99q/H90P3HB9gYo=", "gHoEEHoUui8dtCCen9SIeXkQLeLsXi/0cNhyqjxvvP0=", "F3emAak9HLzye2NgRXeIcdzWkM1GnhPLnlmj2D6RLSo=", "G8UXGy2aVHUT0/0dNTdr2SAwX22tTM4Zjs8HKDszz6s=", "hvvFpoVVQ7XFQlMu1PD2f0gufUqVxiZbqLLdGzSDaZE=", "CbLmUzIKs/iIv8RDa8V1fVgX6MD/mC60NjqHkfKCGLE=", "nukJQR8YwnYiBiPSBRjrbvFeL/MV6VX57Ik6eAWihQc=", "u/jbeYR5/cLsC3ykZhTnXyvHwMJWBgvgG/oYWlUEAKI=", "nS2EbjqjMlCr38kuZb2SnHtNezTYjn2+zEgmSIh+dXw=", "IZR+NZhmKhncNLqbEWQe+V9kzIwohX687k/zcqwk7Z0=", "p8uupBMdk76UHb/Q6k11bkCHM4l8jdApCcv/NSt3RkM=", "IxxdJGFAu1JAIU7MLnil7Su40brxx7LqwKCRcFLspk4=", "AyGxBDDw/4PahT58QanC5w3CWDoeYhbaR/lpt48+mAY=", "U1y1O6ZMifDU9MtMXuPDH9nQlRyoUambmJiXYLyx5yk=", "E6xxhN5ddKE+TQ9EBJiZiLRTuTswWYxEH8GAulAamlk=", "Tizc5zN7UcJUm9tPOjOqEI9VzAFej4l614PmfNWecz8=", "8huYt77cDUB5bxbiAMErVMK0fxRtcNqUDFJ/d7NXo4M=", "QB2+4oApMSnYCBlrQZHUY5mdu22R8UjdzOf2jAAbOc8=", "1VA7o1hK9qmZaymRQwQczDpb1nfQxKX+R0gLxgpqIJU=", "kS1QR/dcZ3B/vTcItg9D5M+mO3ylzMdMxAX74YjN93w=", "e6IYSG70XAJ5xn/qIO4m+kMTbqs40XC3/dIA7QB3v3U=", "QA9KKLhyMdQu/yNNmuUDeqzhAAANjRpkb+uY0G5sXSE=", "sxnSH5j4R0GtQity4O/8FTsWQn4UjRupXvMbrbYeQCQ=", "fPNZHgBM4e4n15qou2X5GeV+sxWHiX+UrIZoLjsCXIA=", "OITyE5kJ6PIqrMnxX2xorrHA/7bfcpNDtYq6ZDL1f2U=", "QYsoagocwc1zi3dxn51M98EHZD86UAO+EbmQVf2aqsM=", "QwDM0ytHCeXb8PqMKsSgQ7GDl4gDMtqpQuJxbGESOpg=", "cZSTTaHyEfzWQnYdCLDESktK+PC7EtnYntKEWcxIWJk=", "8WYuETrxCcbvVCwRsfz1rbRDFTtkMh6s13eMagNj7ks=", "m0WMKl0yB8cbtaDaTf7OcsGBYbb4j5hnizxApTdTbpU=", "6ys3MRPSc7ffidNZgVszF8IDol73+Xc4FU7aQ0Yyd9g=", "rmiCwtyShMwtOZePt+KYHL9tV+cnet2MKJgisU1tDuA=", "Zpqm64VdOs4ZvEMRUMIe4b1o3vAzqjO9QgmYG7QuNnY=", "zTNNLZpuBXRNHaqOINpXTIUJfxadb7z/aTExPZAyNKI=", "GzXjMReQNdtYwJPdTE3jIXhwjbksc0LfpA2FDhCzHig=", "jQXLsIojsBpDbF2X6+z8253+1WGIvgC6WDULye9MpB0=", "/iCI5DBJf0ePVGewOvwSSx4Eo7TcL2pwTq/Mn3FM96Y=", "u9UjVuoruKwmasCBhfOWcNeM6zB7jLwo6tM+D/rnLEI=", "xrlk6UD8ozYZjAO5IjxhaHo0YhaZ0lUBnFGgGgIo6ek=", "lmlgyuwmRjq/hKd/Yc+2WzN0PtjKcTOnCNABWz8U06w=", "/Vtr5wY8ThY3R4WKj3IwJVR5nOt9iEUiDuV7TJDbxb4=", "tMlFV2F/GPvcrq5OWep/heczmhl4fAR/HBkT6OH37kA=", "+BFBh0C/CXaF7IyX3vTXCS2TXT1A6hnhpm5/bLbdfv8=", "XvItUHggRp9ZFsg0N0Pz87gzoZaXuaF5qdlRxw8yCHQ=", "/AQQp189eFMlM7mlFTpjuMgSiPyHBJaG3JzBXHaWDfQ=", "yQBbemtGqDrXjtdmD9MGIADMHfdgwghWAGPkgku6QL8=", "aIp9oIwwP7DBEPv5jhWSLDo2nmp2qAToQlz5qWYwCLY=", "aET7If4VDufnJj9Le/ctpYzJjRDgQ9EF5zL09whO014=", "QFjdNmxRAF9vnawFVJtrVI2AT6ZyfP6hVdkEUls2cyE=", "XZY1pnNRxI3XjjdtcOKv/0sN8othnApo+pkveB2cyTs=", "DeeLF+e3tYMKrA9rENMo7fQTA5znBg1sEjuJnv3Iw1Q=", "fdEsGT0uhbvd8ed/zkhb9424MJqGlqLS+k9+BiBIr3Y=", "PF9Ks0ppUJLXRirs5quTZn8Q6amXighroSYP+LaN9QY=", "oTXwMMaU2R9uVh0mrEBgwcO6/39yUsub9qGlFx/t1NI=", "nWfgT6lGHFkf7E8BGe92k7Vna6FrbcUOWlAIHRb5/bQ=", "CsUxRL2RcxJej0y77NQ1YYSAPC/cdUxgovzjA4zbT0c=", "ffGjHIedGj9q32nFaPDwu/wggW4Cx9+pm06WBaXp0x8=", "gZlp2IwEJiKFXoUXbbDxWiz0xNBf7TG5EcRiB2iwlho=", "qfWa0kyuYC4TcUpNAuZWDYclF5BFjY8kGf2Bx3EhZIU=", "3jSnQK2/Q5x6+ICsPoMMA/azX3fmDxKqyp4ghrywvBI=", "CaJTyIzg5I95nLwAbWMXz1KL/1XpUVu4WlOTDCWjMr4=", "Jo3yGKmJpEaEvARmjQML4AqLXqbffubucUe999q5HrQ=", "EJMHTsg1aK9en+ctDZgYM/SN0mkpj8quoLR6/DaQWaA=", "Ahe7SNjeU8s5nR/opOv9bM46B/tKkIzWRu3hpUGzfFo=", "fYu5aeBvfby7mUCcohj2V2rTv/7FdJ/l7L5KpNo60gY=", "jvux1lXnUV8/DDdc/jBYij3qe8fPBxoJA77AEgsgwTY=", "74xpXq3+WQgUbDls0QKevkwRrTbD31hc9ibrWjsVWp0=", "sh6Sa5vl0slql5r2+xA9snM5fJZ+vpZkyQR8HyNVQ8E=", "bxh9tT43xDDVNQxQiC+iu/JounyFJZgGBk9m3NNKstE=", "KfiAZf9rYIXti3xIN8O4BcUf6zmlDhXtlQqBn5cvLlU=", "XR8gu0YRAf8a/HQKSwzwzA1J9D5Zxdav9iG5hVYCXrs=", "8HKQzj9v7RQ8nmqjch2QIbz7FCsUe3apULi4wVVVUPg=", "yR3fANt83F0VluaM8/0+OFiEfETNfX8xWNYKB4R6XIc=", "jNU6w2jhHoChnAJ4zX0Q69+n+So6PhBaMH6eKNOAY7g=", "zWF0C/3rP2++J3O4+kL/ijUGi/EKesVMFFR704S/HZM=", "KrWre7kq0OOjBn4/JVJg9StNsqHUFNSDk60jbhEF8MY=", "7XDCPpld0YH3trB6ztmgsubjGD+moDRXvMmTj5CEkFk=", "FFgem0U32Nnku96/0TNvFQNoKuv8aZcihp5cKEbhKV0=", "kKMxQ7tGrAGdjxn2MyFe7dw3xlp/e5DaIlu1hBVKbBc=", "nr7PxA5EKy2OBMeOVqhLLrglYnLCY8ltAjsMA8LDUaw=", "6hkwdTG4hG47DY/rLGZvpnY/ub5oN7Ql9fQkCk4tP30=", "FjqwWS0gmr+CLfySzndXaV8G84lfC7Da0hcczPZMcjE=", "EKg6goS1lOn+cwqOnwDjx+kDRHO6Kjabkf2BP7X//yA=", "vO/fqMgn0qEx+627wrslx5NQlEOjXbcPoYnTKGMzJYs=", "qv8P1lgawrVIQ1sBj2TUC/UYIETC/W0RqYBUNmMJj6U=", "Bk9rp4cW6PqVowSTTgLlA9lmBbMW/XmiksKUZw35Sxc=", "O5eZHqmAWMDuyH4vYuKW/62UGdjQx/h4ap1S/Ax7BXU=", "anpmWgQHJnagRQxiTzkTj8E77Dbd9AfODe4EPq/NJqk=", "Qf+j7N+c87lEQB/WxhlhDHebENYg3UZImdfzQSyJvdA=", "9q2IgaHXa1J8MlXBHYsNutTQdcCNwUoHb6txCgqyXBs=", "D9Z0v9U6POpywVzM9erAMDq+bTwlQ+zzGFWmVX6vazU=", "G0fa29nZSstuOrKtc3arE/Lw02XqP2EGg48XTAxWcyg=", "4VbBCWlams9tFXQilDqtTOkAbWfU1VLJx6xo6aa6GZg=", "JqGW/x6t0ZDv7QuJKJHGSzJQmBKnAdmHybohUe5DzyU=", "+VNunfto/gt5JDhQ0vY3D3BTikNcNEqKY0et/v9+2R0=", "hTrtPl3QP0qN/GUWjf7AxLkZ9D/KMmYz4NGp5knAnfs=", "OrceRLc/da0iqO0xK3vrUV2+G9FLkwN8ISO7kmAqJME=", "F51vmCiR0870yflpn3ekr4W7DiHhZCbOUs6s44MjkpU=", "X71J2SkFz8A23Y/nCMWGPUrXd4szcHkiNfOL7PedSTc=", "HC7fratEJ7aW58E8LeI2ZT6uyAy1wmikmQRzwGcE7Dw=", "FeoE9jTwpLDW90Jc6FncH4GgbCmLgNEEMDLB9sEdtQo=", "CgdNhQ1YqeIdZKRa74zvT4WDvUEi4G4WxNFBeTGRDBE=", "9mYheCYueUTfu9u0JodcTj/F4HIL9oxM3aV3T1kqMAQ=", "uQMXHakeTOQ+HUQ1a5RE+QmppJEIS2/phuNFsC1nnGE=", "7BYPN19KIbzsVhdGd3ocwYmym6W3z/Q0npnr5CgFhHs=", "6Fusrq/5rnHSvHUvLXFLLC/w4n92cRKHftcARghD124=", "iL90U+IbX+LCD3Esz6pKJXjcJVnah1WHcYuQo7qrG4E=", "nCsjZr5HlJMGpHZHwKU0tB+vZUaUIJSiKxMNgGQWP3A=", "XdZxWUHz68YY5nB0E3nHLXGvJH82GHxhPgO3LGl2qIs=", "DjLXB1tWSPKfuv8WlGGdWG2d+q9PPLI0VBkw2eCk7gE=", "QWzGpk5NEy2vhXNP4AfMygKbluTZaLMxytZkJIpyNrA=", "Sj3jriOvWgvPgk7SWmj2cGN61+JbprlSdaXUP0nvnkU=", "3r4PsC9UWEH/Od1UkBLZcVDrTz/xe7WoBKo4XMjIInE=", "hazTPLuwg8boUJSxu9Z7SmiKr2giQAItDJuWthvsM1A=", "Ov2cNqGhe5txY0CaiPGjk5RnygSIv5XenCj//bA5Fmg=", "7HP0LKnFj/QjciZv6Eu4KvkWimAaz+UM4lmTYgW7G30=", "ghKPIcR/x0t0POa2lu7aL7UWU68fvQsadNJBXmAmChc=", "6OAIe8VVPGxja7QByUl0qcTlYIIqLKS6uLX/cxYwQoM=", "7TBO5kberbGRVPq4qUOI6owXjA/agm6CjmAA4c+3opQ="], "CachedAssets": {"Z78u9zy1pTw8ztVFe4hp6HpVzCggcYmikTqUvZhmXJ0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\f32kn295nz-gcvjxldlff.gz", "SourceId": "Microsoft.AspNetCore.Components.WebAssembly.Authentication", "SourceType": "Package", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Microsoft.AspNetCore.Components.WebAssembly.Authentication", "RelativePath": "AuthenticationService.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.webassembly.authentication\\9.0.7\\staticwebassets\\AuthenticationService.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "max6bozg23", "Integrity": "Dmk/HbaCkisGr8Vgk5M4ZS0yeQNNs/uoWecA7Wrysbs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.webassembly.authentication\\9.0.7\\staticwebassets\\AuthenticationService.js", "FileLength": 75085, "LastWriteTime": "2025-07-12T08:24:05.1582658+00:00"}, "KD6wyboCIrVgSKIgTvRN6Pjuyki8bdbDHkXAxT7YKuI=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\3025335p4c-804xk52sxk.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint=804xk52sxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\css\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2dggnkzqz4", "Integrity": "1q0SvQqNP3BFFWcZ9a9ABxN9pzw6weGkWpOFviVy67M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\css\\app.css", "FileLength": 3818, "LastWriteTime": "2025-07-12T08:24:04.9752351+00:00"}, "RGCgdjXA7aKnxo5+MhKNZZqUkNjSg5eVFOK2QdNsvv4=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\pumw8t3m24-koj15ey1j2.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "css/app.min#[.{fingerprint=koj15ey1j2}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\css\\app.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bp4gcomoa7", "Integrity": "MTh6vMaZBsTYNY7gg8fH/vgBr9ZAmtqZixGsBlrI+hc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\css\\app.min.css", "FileLength": 8349, "LastWriteTime": "2025-07-12T08:24:04.9802201+00:00"}, "n7SnjHxYDA28JXWLWFwbmPkCL/rNiWj3swcW490YzZw=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\pegugkbth4-drn3s70o78.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint=drn3s70o78}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "62kxlz54l0", "Integrity": "ztRv/67oI9IODsaNLrEqmLN/jfp7Rt1mSkpokI8Fk6M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\index.html", "FileLength": 473, "LastWriteTime": "2025-07-12T08:24:04.9822201+00:00"}, "znM0UvfpjNz0pRjUuPeXIr7Akyc0Xe9P4Jv5A0QrgEk=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\jmtx3uy9uq-bqjiyaj88i.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-12T08:24:05.0002281+00:00"}, "wkgu6rfV3NAo3hGuqhap5EgUK2XNtX3rF34ZNBdA89Q=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\klnzg3k096-c2jlpeoesf.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-12T08:24:05.0092277+00:00"}, "+8T3HzreDft4NllGnPOZde5GKZLd8bDEAsj3QwUZjPs=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\sgoxxe9da5-erw9l3u2r3.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-12T08:24:05.0657409+00:00"}, "xj0nGNOpoo343WS+rryfLy3xlhtNAklPw9K73xC9wcU=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\648yz23h49-aexeepp0ev.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-12T08:24:05.0797416+00:00"}, "MPBZSoEaYEIkjrr5fvBoSc/NW2wjAC8oaggG7N5BshQ=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\pr52atsuai-d7shbmvgxk.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-12T08:24:05.0022274+00:00"}, "xrLkv4Fhy0K2V89nYXXi8oZg8y7uECgLbr9r+tsqP7E=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\x2nksotva3-ausgxo2sd3.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-12T08:24:05.0287407+00:00"}, "FqPmJ1n6KrXMYUOYumYfLB1pW3xFpZ1LXxCvfGZWM04=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\a8kco5gyt6-k8d9w2qqmf.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-12T08:24:05.0337403+00:00"}, "qZMqO7TEL9uE7Oeowj0O80S9aLR8Thi53IFZjF/Emag=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\joz2d7osaa-cosvhxvwiu.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-12T08:24:05.0367401+00:00"}, "itA3UW/SweusSwZ7YJ7zqsYvpEoA2EC2lrW1D3ooI9E=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\utnpxuoaw0-ub07r2b239.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-12T08:24:05.040744+00:00"}, "sxkLYYi39X5LSBlg5F2s6zfaEBjilfPox1bUiNC30fc=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\adv7zkswhe-fvhpjtyr6v.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-12T08:24:05.0467419+00:00"}, "R5lDCV2uOnw0dOa+KBCYgO/nu1sF1MVBVVgGcQ+FsSc=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\jpcw40c2oa-b7pk76d08c.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-12T08:24:05.0757418+00:00"}, "+NwBHtJURLsaFU5mpLGmxXYUSsBJotcd/wjiEe6ZipE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\4j8cs7bg6z-fsbi9cje9m.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-12T08:24:05.0997493+00:00"}, "Jjv+fxKXACdO6NOde4K5UlZ0EzW74hgFtutuA1Vynu0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\wvxsvykhal-rzd6atqjts.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-12T08:24:05.1192636+00:00"}, "XCcd7vSNNyh3OpIvGPsr79bZD3RBRMPkulcJqlxXI3c=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\4zqhc20qgy-ee0r1s7dh0.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-12T08:24:05.127263+00:00"}, "tmbyZf70q+TtKgltS5Hght13Pe5Rl3MQL0dEfijqfTY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\o6frzmej63-dxx9fxp4il.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-12T08:24:05.134262+00:00"}, "8f0qa0OxBVMtAma16Otd/yf21+BQUqcoCcc+YFM8K+I=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\1x3650sv04-jd9uben2k1.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-12T08:24:05.1412628+00:00"}, "emAMrZTU8A2dL0ADHI7PZZkG40eCn3LmK7mHtlbLCb8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\gf9aio8g6l-khv3u5hwcm.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-12T08:24:05.0817608+00:00"}, "K47+Cjz+DsCXAIr021D8aQw2VXIThWkX2EtWj2zbjVo=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\14cigmz3bu-r4e9w2rdcm.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-12T08:24:05.1067489+00:00"}, "X6eJTl0mjZtjlPhK44v60TdQq2vffHEgfMNuCzIBZhc=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\lq165kg4xs-lcd1t2u6c8.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-12T08:24:05.1172643+00:00"}, "hvlHlTemtTB/wkGLeBTLwRTc44i1cn+mgTTCDgWc6Mw=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\6011wagjqd-c2oey78nd0.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-12T08:24:05.1302915+00:00"}, "6JEXMd5Roo/0+4N+dnv69uIkQdq8Q4LmbdCdqtS74cA=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\kw08fyas0c-tdbxkamptv.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-12T08:24:05.1362624+00:00"}, "LVaIr6voax6cj6dv+M/EgCYgqSdVDxp1x2nUVUSv+ng=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\97icbai9fa-j5mq2jizvt.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-12T08:24:05.1642627+00:00"}, "uvLpcz3FcpPKOL+CHfJVTr6sAQh8wdF+p9Ab6G0Lhn4=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\qyu0uf1byg-06098lyss8.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-12T08:24:05.1682629+00:00"}, "Bukiye8rVR+zSu3Aj3mpefC9xS3QNpjF98T9XlQvpmA=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\e9q8mt7vxg-nvvlpmu67g.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-12T08:24:05.1792632+00:00"}, "ed4OzkiFAgqyxn/Qvf6MFIxWLTOjl06gO8TisTGOoTw=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\tzgx4asu6h-s35ty4nyc5.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-12T08:24:05.2307842+00:00"}, "zoMzEbPiiC8l7XgV1C7mX6BU/H1HVunNl5cJRdD8qWA=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\tdhiitqnv6-pj5nd1wqec.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-12T08:24:05.253785+00:00"}, "9ByGx0jsYtFEykcSXG4igJZ1EEr5lsbM1WMnNLQ4loM=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\ckk7o8hu8h-46ein0sx1k.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-12T08:24:05.2597848+00:00"}, "pc9wdcARo8YhY1Su5aF9/Fy1qaVxvnTcoWatsXWgeQc=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\jd9y9g5lf4-v0zj4ognzu.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-12T08:24:05.4087127+00:00"}, "j3BwkK+WA/cL+8L6RYhtXntdGkdK3VBVUHAcuWB+Zvk=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\iu1nrvl3jq-37tfw0ft22.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-12T08:24:04.9812199+00:00"}, "OXO6XG8j3iFP2Sz1FBGFSctsy6SG7qAEBjiz7VHZq9s=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\5gzrsme9eu-hrwsygsryq.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-12T08:24:05.0062259+00:00"}, "fR2Ym5wBZZrKtTrFZhooQ8Ei8rloxiTV3gO6W+TmxtY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\8gaw29dw5r-pk9g2wxc8p.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-12T08:24:05.0157409+00:00"}, "RmIdX9aNQbQ91WTZvAMJ1uV4S4fH0BkQIQBQXwI+SPY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\hp9e8p8x9v-ft3s53vfgj.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-12T08:24:05.0507414+00:00"}, "XS7NLPzzziwafpf1kqAdLwjLDU52EcD6Es0LEiTfG7A=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\cfuxrruyqt-bf7jydvgap.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=bf7jydvgap}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vgf1gxyn93", "Integrity": "ceeIJGesEdvvIZ02lMM+UEP5NDTkhu4daA//Ll3ixXg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44357, "LastWriteTime": "2025-07-12T08:24:05.2092706+00:00"}, "5iglABheIom+jYUKvmMLmd79sodcXTPOUoCWA6hw3vU=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\tr3bo843mg-j95bc3rf6q.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=j95bc3rf6q}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vgy9slkjdk", "Integrity": "QdmyzfhfMGGhyPKS8dyr5dGq0+Oe04Armlpqy5GPIXQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92054, "LastWriteTime": "2025-07-12T08:24:05.2357839+00:00"}, "a2lu/yTQzXQq4DkIWsSVEa+FnLicvdOQm9HrQNrT3BU=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\7meckn6q8r-493y06b0oq.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-12T08:24:05.239784+00:00"}, "QYRmaVF3jvZ8GeodIPGdN2h36XfExUIeIDjsGxwyMc8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\wt8xbfs674-9gcurp3gf0.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=9gcurp3gf0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mp3ojsgg4r", "Integrity": "yguH7T4Vq0AQ0I/Fa2PB4qU/qxpkDtr5WBn6fbIh2+E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86959, "LastWriteTime": "2025-07-12T08:24:05.1752636+00:00"}, "Ffj3pTFlL7PrhnYYcVzT5RBIjOYuZr7jOS8FsF4Xb9Q=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\lwgmvhwb8f-e6dc18q282.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=e6dc18q282}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "16xe3jowuw", "Integrity": "l9EuIoym3GL6N9ielWplczwsiuke8/yt1im4lWLQ8Wk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28856, "LastWriteTime": "2025-07-12T08:24:05.1902623+00:00"}, "b1726SCDbya2m4Y9KNoXjw6Zld3A+W4B5cI4XBroYMw=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\zqbleobofl-7lkir8dail.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=7lkir8dail}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "otxtwnfgc8", "Integrity": "hOOfM3IuHvbfzw7hP8qDDiW0sdgdU5Ub4oDNnb+W1Vc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64127, "LastWriteTime": "2025-07-12T08:24:05.2874396+00:00"}, "rbQok7MMzbnEQx6hMzo/LJN4KY/x0RlbO5AYTrRz6G4=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\alrh8e10jy-jj8uyg4cgr.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-12T08:24:05.2914407+00:00"}, "GPAZ0qPBpQU1/yQzXM6x9qU9vQqcfSEboecqhYBbHJ0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\3srz6cmdr7-w0geey6yz8.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=w0geey6yz8}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fnc4ru95t7", "Integrity": "9eMB2BkzMLeLVt3PC3Qi2Qt/I8jPEtDfM/s8nL2eGkg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56671, "LastWriteTime": "2025-07-12T08:24:05.3495947+00:00"}, "yVD9fpc7V0h3G5Fshzk88JiXl6kaalocOapuzFeNQJg=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\xduq541gqw-pi1z7vwyza.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=pi1z7vwyza}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h8wur2db0s", "Integrity": "sdHv46r/SZg8jfofaFo32ZQPMqU1iX0wuSCu/91+Q6I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29572, "LastWriteTime": "2025-07-12T08:24:05.3561108+00:00"}, "STCy/Z5+oYKNU45J1jSzmqx8hbvW5wJ2u/zN2GduY30=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\5ys5v13ku3-mxjqfek9l7.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=mxjqfek9l7}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ub0fufs7vc", "Integrity": "aqkTrCA40k8nIyhifP2sT3OWcxGHuI+ipxj5atDNxog=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64429, "LastWriteTime": "2025-07-12T08:24:05.3706301+00:00"}, "meFhxgQ7dxerq/CuoTkjA1SHD8z/5MM3B+4ZCCOhRVQ=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\z0x4v0olnt-63fj8s7r0e.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-12T08:24:05.3731361+00:00"}, "RvXAQAaqKxoMcdx2aucT9ecp3IGevs9u48lfbPAJlZA=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\4zgo1z6ovu-qkwedidd64.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=qkwedidd64}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42e4yhxo60", "Integrity": "1IkzkvPDLFmq0CNk+xMeWnq8oXfzSxgXId0bN+M2EPE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55851, "LastWriteTime": "2025-07-12T08:24:05.4438304+00:00"}, "C8nkN1czbPUk4/2Oyg01pFPg6HwaljY+7QvfAZz2v6Q=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\4av10ibhhd-iag0ou56lh.gz", "SourceId": "CodingZoo.Client", "SourceType": "Discovered", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "sample-data/weather#[.{fingerprint=iag0ou56lh}]?.json.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\sample-data\\weather.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4cbitfuojg", "Integrity": "HD3vAUwurZXW96vdgG5RVLhMjmVeSgBs4iKLSw2+Uwk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\wwwroot\\sample-data\\weather.json", "FileLength": 153, "LastWriteTime": "2025-07-12T08:24:05.4448311+00:00"}, "OSXkAbTVORhu3DVbUbacQe4BCASOkRu0HjmOR7GgqDc=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\q0e3ypovth-md9yvkcqlf.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.webassembly.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.webassembly.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k1uijd3xue", "Integrity": "aODHHN99ZO6xBdRfQYKCKDEdMXpBUI2D7x+JOLJt8MQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.webassembly.js", "FileLength": 18128, "LastWriteTime": "2025-07-12T08:24:05.5661868+00:00"}, "hHG2lrSJYPrnHzBccRKfWpqGkWuyoEdIazQDiYqnfG8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\2kvt1ahbvg-5s6xfy9pfw.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "CodingZoo.Client#[.{fingerprint=5s6xfy9pfw}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\scopedcss\\bundle\\CodingZoo.Client.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i17d0lbdir", "Integrity": "5sglLvuOnEcTmfATyHO3zA4n17H2kCPm9sb6s5i6Ai4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\scopedcss\\bundle\\CodingZoo.Client.styles.css", "FileLength": 1399, "LastWriteTime": "2025-07-12T08:24:05.5681887+00:00"}, "2vr/luuFlBZDIIgTV7xlfbZS8HZcWNpA2HuGqwgVko4=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\3c6y5vybft-5s6xfy9pfw.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "CodingZoo.Client#[.{fingerprint=5s6xfy9pfw}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\CodingZoo.Client.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i17d0lbdir", "Integrity": "5sglLvuOnEcTmfATyHO3zA4n17H2kCPm9sb6s5i6Ai4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\CodingZoo.Client.bundle.scp.css", "FileLength": 1399, "LastWriteTime": "2025-07-12T08:24:05.5681887+00:00"}, "OnDptIUvOvKLJaVHxm9dXqY1MWU0NVyBkVvTq26F71k=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\5b1p66nvjv-bvu82j4ad3.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Authorization#[.{fingerprint=bvu82j4ad3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lgnzgsjvnc", "Integrity": "hVFMa3gj6Fp9MYa9SnE/zNxKKJm/po/LamQvLk5l5Mg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.wasm", "FileLength": 18074, "LastWriteTime": "2025-07-12T08:24:05.5701855+00:00"}, "gghhNLJFbTwU/z/ltkJFK77MXbqAKQa2GJAmyHFIy28=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\e6hdjo9fdr-ptfrz3fits.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components#[.{fingerprint=ptfrz3fits}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nx0qdkw033", "Integrity": "O4SF84iMUCIU777WVOHnz/CBIrJcGjQJ+Sfub12Mous=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.wasm", "FileLength": 135114, "LastWriteTime": "2025-07-12T08:24:05.5842146+00:00"}, "ozMFf1Crrf8sbf59OK++7gofRFUV7mF2TrHy8Mo8gEM=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\6urrn3kh88-a48sropenz.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Authorization#[.{fingerprint=a48sropenz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Authorization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rpntdp9o7m", "Integrity": "T94YMr59q/9UMFhz+70AAgqZryMBD7MzBpQGj6XQKjs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Authorization.wasm", "FileLength": 10090, "LastWriteTime": "2025-07-12T08:24:05.5852155+00:00"}, "fSSt1mSrx9nWZdMdU9l8E65BgohgDkbERAsZGVn4tc8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\xgzqixzzbj-73oi73dvgk.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Forms#[.{fingerprint=73oi73dvgk}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "11xxqegnuu", "Integrity": "i8hp/sm0+RvHSjMM1tC7dQNHAIJknbBR2AAbUcApBj4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.wasm", "FileLength": 16726, "LastWriteTime": "2025-07-12T08:24:05.5872233+00:00"}, "yN1UnvS5KQc1/GiIQQ5/5nwU+R3+ikLqv3XJ7qMo80Y=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\2xqbyd54fe-pm8mpy5cip.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Web#[.{fingerprint=pm8mpy5cip}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m77mlbd7zp", "Integrity": "/mDirF3iNsajE2C4tMEGSTrDOEJ+fJbLRW38GFBuAzA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.wasm", "FileLength": 72595, "LastWriteTime": "2025-07-12T08:24:05.5937333+00:00"}, "8I4IJ2EmsTJP0f7ZDagMwbhgLZkBQvZg61edgpYppzk=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\82guxesqid-h6ioronled.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.WebAssembly#[.{fingerprint=h6ioronled}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ijwhpv5p2j", "Integrity": "Ahx7fiRa4p+0d8du7KStExXiCwrIGXHAzS5EPF7DSXg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.wasm", "FileLength": 67490, "LastWriteTime": "2025-07-12T08:24:05.2703016+00:00"}, "Z+9xzWiLqL3APgIP1gdAt2GyqA8OgaZEeml1H1r3vYo=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\74xwo2vt0w-njkmmuc5op.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.WebAssembly.Authentication#[.{fingerprint=njkmmuc5op}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.Authentication.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z6c091c371", "Integrity": "uwq8Waraft5kvMrDOJhpy/YMxG+QVqrk1gVDqPYUoqM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.Authentication.wasm", "FileLength": 39144, "LastWriteTime": "2025-07-12T08:24:05.2738104+00:00"}, "q79R5ch3hYXyI9jdozM80u0fwP16TD8WoOCM234L97k=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\spgy6oggna-eyher82q7e.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Metadata#[.{fingerprint=eyher82q7e}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6cciwrafzt", "Integrity": "9toD9OdoDfBmSvIEZvApCUsWcyyHrx7jgfRc7/Oz54A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.wasm", "FileLength": 2432, "LastWriteTime": "2025-07-12T08:24:05.2763655+00:00"}, "1yXYfPTaD7kmkCTyDhjl/8qqyPduXvR/Fz22KOqsEqQ=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\q40zrhcuro-h2p7847z2f.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration#[.{fingerprint=h2p7847z2f}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ilz0o36izl", "Integrity": "yme24RHQ7uWRTehrxyTqFEi5r6cH4FuL2u0lW225EUQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.wasm", "FileLength": 15910, "LastWriteTime": "2025-07-12T08:24:05.2778785+00:00"}, "anJq09GkslIjA4wGT8H+OfnUV2oXlT/vFHDJyb2lL8M=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\qwzbmj1as0-qfj5950lp1.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Abstractions#[.{fingerprint=qfj5950lp1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6dbvgyu8g0", "Integrity": "tvx/HKSybKWoM7ZRE40oaXi34QnDChk2ZuVa2BnMbWg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.wasm", "FileLength": 8473, "LastWriteTime": "2025-07-12T08:24:05.2417849+00:00"}, "DnYZonfMxdmXQMH7qcx9BEExjGHsb65tjIaCy7H2tYk=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\bi63a0v0yd-v8ihxrft1m.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Binder#[.{fingerprint=v8ihxrft1m}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ozkyunwgcm", "Integrity": "PMeXg9t7mG7j0tzztsd8PfT3Ruz/4aSnYfOTrl1cErs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.wasm", "FileLength": 14898, "LastWriteTime": "2025-07-12T08:24:05.2437844+00:00"}, "E8KqCno++xi98V4CRg3bphJvA79+B/iA7vB9ZgKMJ1c=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\6l22wcytic-jgemvlpy71.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.FileExtensions#[.{fingerprint=jgemvlpy71}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u8f3b62sm1", "Integrity": "Eq1sepPAvGGWcfZ/FXW0VbJCrqbWgm0czZk3y2FVeBo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.wasm", "FileLength": 8401, "LastWriteTime": "2025-07-12T08:24:05.2447847+00:00"}, "Vx7W7ubrx1e+aVKArTQN7xbF1HF8XQLjF1Wui8/0R8o=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\dyfdpw7qw6-nn2igycfnm.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Json#[.{fingerprint=nn2igycfnm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lu0ylxyefe", "Integrity": "M85PMWJVkGaxzop7jtuwLkNrDlNriFZ5sCnY5dMUg+E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.wasm", "FileLength": 8204, "LastWriteTime": "2025-07-12T08:24:04.9752351+00:00"}, "zhJx6zBD3vovetbyL/DvKgLpOyDw+9omlJeO+ci4KT4=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\1ttdyxz28s-v66dtpac4v.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection#[.{fingerprint=v66dtpac4v}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zu3s69rjil", "Integrity": "dQMnjJd9TtuN7I6Wq/MEe8lO5zpD4JkUNGi2LjtVeN8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.wasm", "FileLength": 36333, "LastWriteTime": "2025-07-12T08:24:04.9853437+00:00"}, "5LD1R6Of78FIbnpMJp2BGRABbzfQQ8skgJDL/NOAeC0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\zp0eiap596-apuz8nsfml.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection.Abstractions#[.{fingerprint=apuz8nsfml}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4f0zd07ryb", "Integrity": "ILbxjNyM+WKKf8bJtkfwEQ0MzZdc8C1xCl0aEGConxI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "FileLength": 22002, "LastWriteTime": "2025-07-12T08:24:05.0092277+00:00"}, "0c05DMMJTJMPkw+TT0vcB9SrLDc5QHedar/JZE0SmAs=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\meqr20cuvc-t0xgg5v1wp.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Abstractions#[.{fingerprint=t0xgg5v1wp}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0anp09lt9b", "Integrity": "6Gl/HUNwoAfvEKO09YcfAVHWuAox+8dsQ6HPGF65vug=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.wasm", "FileLength": 5725, "LastWriteTime": "2025-07-12T08:24:05.1112559+00:00"}, "iS6126o6RpIWhLhd9AcmlSbPrkMARUO401Qr2pJ0QdM=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\ryo78cuuhi-k8mchckl5y.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Physical#[.{fingerprint=k8mchckl5y}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mqv1rf7kv8", "Integrity": "EqIFyoS5iXLv3WBecVHWA8WpwvRHBlfb8fDXetQeuq0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.wasm", "FileLength": 17356, "LastWriteTime": "2025-07-12T08:24:05.1172643+00:00"}, "bNN4BEC1yuw+0xitLaGx19nA6mRExuOAGiv1e4xIGhw=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\xp73g4f8f9-whx15sk3zg.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileSystemGlobbing#[.{fingerprint=whx15sk3zg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3mijfn4fza", "Integrity": "+w+Q4GZv/z0lG+fK+EPO0e6ZJQl1Q6UtMbu3rYzZr3U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.wasm", "FileLength": 16773, "LastWriteTime": "2025-07-12T08:24:05.1202639+00:00"}, "yd+hZ+tDE4BPJ6NRpbQmPBCJkJyvdsF0UIU8dvX8xAY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\umg2y6p8v0-r19g4upt09.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging#[.{fingerprint=r19g4upt09}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u5ztklcv83", "Integrity": "viBYvEPN09y6MBHR7WY0eY+n/FMwYjRWOeuX9/bIa7k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.wasm", "FileLength": 19459, "LastWriteTime": "2025-07-12T08:24:05.1262647+00:00"}, "C2w2rPJP7WHuZs2l7dMcSll1chrxGucdKDLUpFY6bng=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\2arppgcgam-nwxyu3e2hm.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging.Abstractions#[.{fingerprint=nwxyu3e2hm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nl2ri78u79", "Integrity": "6xMK6hCgvV14x4djAKXx2ijv4Dej5/en2cdDJZfHK/E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.wasm", "FileLength": 25077, "LastWriteTime": "2025-07-12T08:24:05.1382641+00:00"}, "+9N1MaIRKVsP5ZXBpooIfccgmL+6ZO+XlATcq4o0i6g=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\m7hidjzusc-l36scmr1xu.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Options#[.{fingerprint=l36scmr1xu}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q10tx7tree", "Integrity": "fnKYyd2gVHBofA0jRPJTWBDeklawripxqtTIwhyTFlU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.wasm", "FileLength": 24185, "LastWriteTime": "2025-07-12T08:24:05.144265+00:00"}, "kjP84EiOqxqZFu4ciifbcRbO8LQ62IQYdmy9OJtCLU0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\5pyxyfllq2-358c2dzezi.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Primitives#[.{fingerprint=358c2dzezi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6xdmz3gb1h", "Integrity": "+p7TJJz9QvNnk6XFeNrWRl7+GS5iOX/R/sE/opnIURk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.wasm", "FileLength": 15651, "LastWriteTime": "2025-07-12T08:24:05.1672648+00:00"}, "JmIVKymzK15iV069onIY352QLJfpcCKcX7LKcNTfyMo=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\dan1w933ar-nanjlpvyw1.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop#[.{fingerprint=nanjlpvyw1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yr2xu0mmkq", "Integrity": "Tkqp1lGVMdtmMlVOw/6U3fxmGsCiMa2bmV0MAp6ZU+8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.wasm", "FileLength": 24126, "LastWriteTime": "2025-07-12T08:24:05.1742625+00:00"}, "LuSTMZoSkCwf0ltxw/7PDLZ2BS7L1te09L6zQ4Kshxw=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\afta341f0f-v1y0duelx8.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop.WebAssembly#[.{fingerprint=v1y0duelx8}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9ihvfdpe3k", "Integrity": "YT0UVFPVRBpKeD7gamA5oUbq5Fq1CtUYfuF6u5gI3q0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.wasm", "FileLength": 5794, "LastWriteTime": "2025-07-12T08:24:05.176263+00:00"}, "qVEu22/Dp0YdRF1zsnkU275aQKvKWZpfjC2ukJEhQQ0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\yqc9kqqfcl-wjcmf9ahqt.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.CSharp#[.{fingerprint=wjcmf9ahqt}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.CSharp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "whn2qrvdkx", "Integrity": "dLk/NeTPAMoZXQ7+kQfLn6Tqdo7TvGxUu1wV+F1Tpsc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.CSharp.wasm", "FileLength": 132481, "LastWriteTime": "2025-07-12T08:24:05.1912624+00:00"}, "Pc5Zqp5wx2SJ3DyYnzLrUmR7l/K+C4EHHXm9v+ilrG4=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\qzqqd5k43i-ccpi3o747q.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic.Core#[.{fingerprint=ccpi3o747q}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k6unbd2stq", "Integrity": "jFcHVfm9QLVWCtisUrkbLfVXBvSYsiOsz3R3gwhFuaQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.wasm", "FileLength": 171163, "LastWriteTime": "2025-07-12T08:24:05.2728041+00:00"}, "DMMf0Oz/N2wvaDsvogxVLayYz+NPB5vDEWRxJQr+uUI=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\g04v0whbsy-pbpr2ghqk0.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic#[.{fingerprint=pbpr2ghqk0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gxddmsgt2e", "Integrity": "xI2vA0+H5JFItBQiIcI+2x3g4g17Ec7bJce0b65Hgzs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.wasm", "FileLength": 2874, "LastWriteTime": "2025-07-12T08:24:05.2728041+00:00"}, "V6QBwrSxOJJjuinJzkeyIaGSRjt7c/fzINTYyxRrQCg=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\gahunrrce8-dqwofb9ps2.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Primitives#[.{fingerprint=dqwofb9ps2}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fnmlg52jej", "Integrity": "FXY6BsQcn2XoiSqQemKGsSvKr+iX6h4VrmD7qOD/m8o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.wasm", "FileLength": 2198, "LastWriteTime": "2025-07-12T08:24:05.2447847+00:00"}, "Fh6Zg1Zvi9o+uQq5YipIIhrDT+XQBDDlpRO+8T/vWtI=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\7u5xejdj2d-rcu343ydbj.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Registry#[.{fingerprint=rcu343ydbj}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8lzj6pew6w", "Integrity": "FBvNldlj2Mv/YAElTqKc4IdUZlvAyxQ9yAPvqg8OdX4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.wasm", "FileLength": 9272, "LastWriteTime": "2025-07-12T08:24:05.2457846+00:00"}, "0X4ZZJNLFQvxadw4UbMV1ZaXqlX/OxI4E/152A4n/Vg=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\z0pv1o0h1v-00rm2k4rqj.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.AppContext#[.{fingerprint=00rm2k4rqj}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.AppContext.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gbky2dbmvb", "Integrity": "XGFQiiA2iSU/jhMtWd8tYiWYL9t4l9Avpz36qIveoDU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.AppContext.wasm", "FileLength": 2098, "LastWriteTime": "2025-07-12T08:24:05.2467852+00:00"}, "ecTMtFA75e1va40do+R2Q6rbrvxa5hbS9vWUpZ9axbw=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\cd00hu2pah-z32pttvtk4.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Buffers#[.{fingerprint=z32pttvtk4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Buffers.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w8eef0mofq", "Integrity": "oA4D9hmv1BEvfv5qORgbbE5hMkdx4uhVRpjjTWagIDY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Buffers.wasm", "FileLength": 2102, "LastWriteTime": "2025-07-12T08:24:05.2477854+00:00"}, "E5BwUFTodYSea/vbCAwUQY98u2jTj3IznWn8f3UcAu0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\cda4ejdpm0-rx21q3n11f.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Concurrent#[.{fingerprint=rx21q3n11f}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Concurrent.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0i67dnlhhd", "Integrity": "qJXKIYFSSNFCA84oRN0q17gXLyqXbWgBoYqTgOVX5D0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Concurrent.wasm", "FileLength": 34481, "LastWriteTime": "2025-07-12T08:24:05.3811496+00:00"}, "a9TGqZU7cn63W2Jk/nzpLGhiA3q6mQo1B9EZq5u4GhU=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\c762vagcya-d8bqqugfwk.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Immutable#[.{fingerprint=d8bqqugfwk}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Immutable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhyv6nchce", "Integrity": "XkLA1HoKdRMuat0Rys6Eh5T9R7ihmG+EQjQqSJ6C8DU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Immutable.wasm", "FileLength": 100292, "LastWriteTime": "2025-07-12T08:24:05.4142245+00:00"}, "qBXv5+vtMmPk0zn2X8wZZ1ncDg3r6znfyn1Ldu/qE0c=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\b42o9kiwdx-5ray22bvhs.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.NonGeneric#[.{fingerprint=5ray22bvhs}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.NonGeneric.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bkdv7bmfz6", "Integrity": "a+kqxOHfjR9qcvERcBAsjkNFWm7dL3AZik9ZJwOH7uA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.NonGeneric.wasm", "FileLength": 14906, "LastWriteTime": "2025-07-12T08:24:05.4162308+00:00"}, "7Bi5T+EM9oODWNYl2G/zbl+u8b//c9qKCue3mxezrE0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\jsmp5gobay-i9b18l2jwg.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Specialized#[.{fingerprint=i9b18l2jwg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Specialized.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "587qlqjbxq", "Integrity": "FXSFbYyRSkg7WMFXEL2jXHL04e5gLLZiSPTvvD8Gohs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Specialized.wasm", "FileLength": 16545, "LastWriteTime": "2025-07-12T08:24:05.4192306+00:00"}, "lrfn2Ib3mYqQGNx4i5LqlA3EYN3rPbGJPfz2GusLM70=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\mawoz1aatj-r2s7n2sx3x.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections#[.{fingerprint=r2s7n2sx3x}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sc0urzraoy", "Integrity": "tAttvLK+Dkz7TUD3bHWMLlzvCmk7N6Q7pZKML89FbNM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.wasm", "FileLength": 49318, "LastWriteTime": "2025-07-12T08:24:05.4247455+00:00"}, "6uYScJL6xn8SjemrTqOJ7MXm/G6qpqWDNCtewfzgqrU=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\n0ap093uze-shw18la3r8.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Annotations#[.{fingerprint=shw18la3r8}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9dsz96d69q", "Integrity": "XUu6ZwDlA3BaLbNMn5o8uVYlU+3CHg6oM6RouNTXcBU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.wasm", "FileLength": 36237, "LastWriteTime": "2025-07-12T08:24:05.4267524+00:00"}, "Zt60htbiXFG0GVtQxk1xR5NItf/2CE2B9wENt4WBQMk=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\u5uvq2y7gk-3tfviggf6l.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.DataAnnotations#[.{fingerprint=3tfviggf6l}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aiu5067y09", "Integrity": "Tg5KQ6dDGE/PUnLaUs1wulH7OmsM11j2bhd5E5LolwQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.wasm", "FileLength": 2574, "LastWriteTime": "2025-07-12T08:24:05.4277535+00:00"}, "cIGDTN0Uc+pdChWfeeAxLORKPr8gqWLtGzqvfWSQds8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\dpsey87vjy-yssxj4mc04.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.EventBasedAsync#[.{fingerprint=yssxj4mc04}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4c1xewlgtm", "Integrity": "BCPI3RteE/wof6nY6ogvkzvVAiw2ZwvYhMsfWee55PA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.wasm", "FileLength": 6877, "LastWriteTime": "2025-07-12T08:24:05.4287624+00:00"}, "EUMHUjAtPzLqC/t3sj0S7RDDfrg2/g4sptNZEa3fkT8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\7mz35tdxhu-b2gyl5z8cy.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Primitives#[.{fingerprint=b2gyl5z8cy}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "se3q3ak7s4", "Integrity": "PCHpBiK0R7POCERG/FQbPCgnTubMzKy9YPQDr//lDKM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.wasm", "FileLength": 13564, "LastWriteTime": "2025-07-12T08:24:05.4307514+00:00"}, "nn9neqZdf3HFkMivfGHCJf1OlEC8oNpk/ykNS+9ZN5o=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\kvpnckwq7j-o2rbwek366.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.TypeConverter#[.{fingerprint=o2rbwek366}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6iufqo47s1", "Integrity": "0v0qzKjLMpP4glPJTjPU7rEQnkuVkGcSXFAmSQJXoLc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.wasm", "FileLength": 124649, "LastWriteTime": "2025-07-12T08:24:05.4828978+00:00"}, "sIfblPs8PIgX7DL2DBCc5g4NYup+W1HjFzKlTLp66xc=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\9vnwzp4c2d-ijpzmqvdg2.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel#[.{fingerprint=ijpzmqvdg2}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fh7l3ppj1z", "Integrity": "I1Aq1Dc1Oo8DYWZ/kW+O8up7wIVQVbvq0gXyTn2po78=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.wasm", "FileLength": 2565, "LastWriteTime": "2025-07-12T08:24:05.4859117+00:00"}, "YlSFttV5vNVdAWZGmBF93wUvL0tm0V/VCuGLHPUAdoQ=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\0t5ur3gry2-s48boytfg6.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Configuration#[.{fingerprint=s48boytfg6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ryjlal1a0g", "Integrity": "U6bMQhPCth9g5LpZH/RZOtSLbfVBtYZih2mrdSUNHK4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Configuration.wasm", "FileLength": 3136, "LastWriteTime": "2025-07-12T08:24:05.4869177+00:00"}, "4bVpRAi2+X2rNP6rxtKecMMqICRHnxNBDnsGhv89aKw=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\fhobdbiq10-0c362v5xxq.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Console#[.{fingerprint=0c362v5xxq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Console.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yd5s3b1x79", "Integrity": "RkmGbj8ZnSECLn2b7+62cOCy9mYfacZq6oXLvPaYml4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Console.wasm", "FileLength": 19991, "LastWriteTime": "2025-07-12T08:24:05.2753657+00:00"}, "DYInXy9zcem73OpkYUdUR91rEKSAeqGjH4p0r9YC+g0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\eivmf0q4kp-aiaao2jpd5.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Core#[.{fingerprint=aiaao2jpd5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jxxnlpguoa", "Integrity": "eTue0iMffna09Z1hFfpWHOmU3nmxH+OSbXU06A4IXN8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Core.wasm", "FileLength": 4597, "LastWriteTime": "2025-07-12T08:24:04.9762209+00:00"}, "8md1dTlSy4z6i9GTY0oQ9B/Uk7uaXiMbnZhVuogNmKY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\mspikx2lu2-lcca6mlcc9.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.Common#[.{fingerprint=lcca6mlcc9}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.Common.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bndvkguu89", "Integrity": "KXVTZ34mhkbrqfqx2oIN1qNdkTUQ2fnX5XmIZyNP3Es=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.Common.wasm", "FileLength": 378863, "LastWriteTime": "2025-07-12T08:24:05.0147415+00:00"}, "gEET6LbgZXPQgwcWcXGqH/2aSqUVPAz61FUsLFWVN58=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\2y1fzi76ta-8vc4lkhdbq.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.DataSetExtensions#[.{fingerprint=8vc4lkhdbq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "epz5zlsyrp", "Integrity": "/gRySnjVGGyHAJWONxTZztnO715UQU8T7zyNQLBS62o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.wasm", "FileLength": 2061, "LastWriteTime": "2025-07-12T08:24:05.0337403+00:00"}, "neGxEgrcHK1cHOJL+pyKdikE7y9+WytUORl/d6xfpGE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\pi5nyfuhuy-zn919kbv52.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data#[.{fingerprint=zn919kbv52}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w2qnpfbgi1", "Integrity": "yOhwSK2HejpWevbXsmbDrSNfN8CaeR96Y/Oz6P4R1/Q=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.wasm", "FileLength": 5066, "LastWriteTime": "2025-07-12T08:24:05.0397407+00:00"}, "6r3nAv/0OihGBkWmmg2Hn6hJa74aNxFtSfYklPjnyrw=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\ibialg305t-q6kkw3vdcg.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Contracts#[.{fingerprint=q6kkw3vdcg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "umbp5jmvjn", "Integrity": "JnjQvWWvQGA1b129nERB5Omtk3fVhw0nM2LanT3WZFY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.wasm", "FileLength": 2390, "LastWriteTime": "2025-07-12T08:24:05.0467419+00:00"}, "Weg202yGrU/hr5LJJ2EKmTU3urytxqtdxa4F1FBaSRE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\5w45hjgnx9-2ahnaecxp2.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Debug#[.{fingerprint=2ahnaecxp2}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Debug.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vxmwgbwzc1", "Integrity": "Ixg7wld6kFJzTcOto8TsCfWv04NYMU1FmuvGiSS1Srs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Debug.wasm", "FileLength": 2270, "LastWriteTime": "2025-07-12T08:24:05.0647416+00:00"}, "VVSs8QLplodxxAqC3zzeeSoB0CknUYnoKDvgwyictXw=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\qyrifvvjbp-g6jmcgi72c.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.DiagnosticSource#[.{fingerprint=g6jmcgi72c}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t0fcto2ywt", "Integrity": "DRTmxn991ElmuBvew4H2E17LlsakWjeu+9dHY6vadvY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.wasm", "FileLength": 74367, "LastWriteTime": "2025-07-12T08:24:05.1252649+00:00"}, "JF/5vfxoy2IpEJdy+7AVDvQDguZkLsk08zPEcX5nnvs=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\1lmqt1x6qt-vpthu2a2k9.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.FileVersionInfo#[.{fingerprint=vpthu2a2k9}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y0g8pmg425", "Integrity": "3ivvr45tcunKnTnWkuKND4+nqKHue3CpTgQmDApWr3o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.wasm", "FileLength": 5159, "LastWriteTime": "2025-07-12T08:24:05.132264+00:00"}, "hvRYg/ws98fxNbApYccCUulfOP2eTLdxnhbTzLALSt8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\a5fpl06twq-agxw6p2exh.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Process#[.{fingerprint=agxw6p2exh}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Process.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5cqutv7qac", "Integrity": "1ekcESQu9PtrK44dSbLgtSqBq5bCjzaSpT5vyqLqeKg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Process.wasm", "FileLength": 16549, "LastWriteTime": "2025-07-12T08:24:05.1332625+00:00"}, "m0UXmhMn7MR18YErthieBQvsbQKi1Um0z/4ObJZk9AA=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\gpfiztb1lj-r4u6i8kfog.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.StackTrace#[.{fingerprint=r4u6i8kfog}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anfijay65x", "Integrity": "thpNCsGdaFZ7TT+mGanvAig3cWI5/oXyNsNGikJ3JoA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.wasm", "FileLength": 7500, "LastWriteTime": "2025-07-12T08:24:05.1392642+00:00"}, "P8JH6eyeBjawkDMmtpV0CQ8PwyuaIylBFkxsmHyEtAw=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\pnnpoduvy6-zuba91cmsq.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TextWriterTraceListener#[.{fingerprint=zuba91cmsq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ph4twsscl9", "Integrity": "/Yscl2e7VRBBZ+EiKxK19ZLqJh84BBTZP6j8t4ZLIpo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.wasm", "FileLength": 9522, "LastWriteTime": "2025-07-12T08:24:05.1432653+00:00"}, "fbms1AY9xIZHbhEyn0YjOUS5WkhGgCfpE3ujVk7oLj8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\6ryod0p28p-t9yjdk35pr.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tools#[.{fingerprint=t9yjdk35pr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tools.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cdvfaiztpm", "Integrity": "cHEne9SK8rrP+xPc+bzJTg/JNRqX7uF4xJTi17nZyRo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tools.wasm", "FileLength": 2175, "LastWriteTime": "2025-07-12T08:24:05.1662632+00:00"}, "4gtTfR/oHy1o4JD+kbmg8NOixu8g788gf5vOCcMUAJE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\h31x2k4mb9-90w5tp9zha.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TraceSource#[.{fingerprint=90w5tp9zha}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6bulh20l6q", "Integrity": "ssUBRqgc2MJe2SyAT+YJB0zgJxH8nKu2JYj4hQnaWQ4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.wasm", "FileLength": 20417, "LastWriteTime": "2025-07-12T08:24:05.1692638+00:00"}, "wMOWg1v201bjHkhjc5U3NzLZ6RzA3PfGKapkwqMghg4=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\jf02sx7o8e-uno27yk9k6.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tracing#[.{fingerprint=uno27yk9k6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4rhwxvk649", "Integrity": "4H4pa1Zb+HJlXKxPb2EJNkA1Uiq3PYZUA5Q/Osa0zCg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.wasm", "FileLength": 2498, "LastWriteTime": "2025-07-12T08:24:05.1772635+00:00"}, "fc6Gwd63Vnf2GirrdRwHC7euxVUSxDMt5J7aOFe7w/s=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\2ra761kcqr-nzdrnpuduj.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing.Primitives#[.{fingerprint=nzdrnpuduj}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bzthh40wc9", "Integrity": "+QxdqoH7R2P7/53w0wHo2JHERi5JXd0WzxFyWKSj1MU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.Primitives.wasm", "FileLength": 24545, "LastWriteTime": "2025-07-12T08:24:05.1872609+00:00"}, "zmeFArWjWYk8rbyMRbX7vJY7mrnsU3Jn0R5Gf8NhHT8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\5tth2rhug1-egkesygmuw.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing#[.{fingerprint=egkesygmuw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "csfg28pnvh", "Integrity": "FJqYuxMaAQeGxiDsZAa6CzJOps6i+oNKnk36wJ4XPjw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.wasm", "FileLength": 3885, "LastWriteTime": "2025-07-12T08:24:05.1912624+00:00"}, "Sb5KZwlYGJra5U+OdYioQOy7kQt11nxke6k2nKA5vQc=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\ncr3wj1nqx-efxlzpwato.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Dynamic.Runtime#[.{fingerprint=efxlzpwato}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Dynamic.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3f2zw98yjn", "Integrity": "/9ro6NA9eSZOycX49SCvAK8MSSq35bJD9MDw8DtPBmA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Dynamic.Runtime.wasm", "FileLength": 2437, "LastWriteTime": "2025-07-12T08:24:05.1922628+00:00"}, "+SoI7/FQ0D8dg3uCPS+JPWiVA2MmMaH9WkTWX+4KRr0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\jfcwq6qpuh-xxh459yzyl.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Asn1#[.{fingerprint=xxh459yzyl}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Asn1.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qg933724kw", "Integrity": "Vi6L8nHtINGY8UNUSMcbswST2adk3eeAuAVc3rnz2fw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Asn1.wasm", "FileLength": 35944, "LastWriteTime": "2025-07-12T08:24:05.1972707+00:00"}, "8dwPz8bs48HoF3GCZqJvSnI3azJr7IpfmLDMd600ZP0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\rgq1hr3wct-qx19rhlhxw.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Tar#[.{fingerprint=qx19rhlhxw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Tar.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42lxtx6ijk", "Integrity": "JNZzTs20B2X5ESynC8UJ48206tQsbFUERJfIESuL9r8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Tar.wasm", "FileLength": 10569, "LastWriteTime": "2025-07-12T08:24:05.1982901+00:00"}, "9azLLexF5QCUhQUebRVvmQ7yStRNPQ5DNTCQzAkrBJA=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\yegfe9vypl-8s8o2vqz82.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Calendars#[.{fingerprint=8s8o2vqz82}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Calendars.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4se6s4hhex", "Integrity": "rPOuUEg5GRqoeOIFtfkDvsleWjnkoHr2U4qsZFE0ZNQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Calendars.wasm", "FileLength": 2286, "LastWriteTime": "2025-07-12T08:24:05.1992715+00:00"}, "ODpYHrEsYpnFzmQw0x+slOWAEp7mFf5j82Fnc41VglA=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\25dsm91j5z-c5jbxrm4w6.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Extensions#[.{fingerprint=c5jbxrm4w6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3wvwpdptbn", "Integrity": "jbkg3ibmnYPvOUIB7v6osTsGwOLGoKH4x8Z/p+2VyVs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Extensions.wasm", "FileLength": 2166, "LastWriteTime": "2025-07-12T08:24:05.2002704+00:00"}, "VpbpPoK7tIPA2zhvAX3I723TZnBhTx5IywNG9aY1J5E=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\hfiaewd7b7-up27xu0uxp.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization#[.{fingerprint=up27xu0uxp}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rw01wp4to1", "Integrity": "214E7tvBvXAfnuLGg0+1+rbzxXkK0CwmBnTad6M+mdo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.wasm", "FileLength": 2255, "LastWriteTime": "2025-07-12T08:24:05.2002704+00:00"}, "uo8DA0IAKVEZrRYIoM2ld3UuLdP4oyg1heOzb2VvnZg=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\9ml6n2uyzb-797644ybc0.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.Brotli#[.{fingerprint=797644ybc0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rcvdnzixr4", "Integrity": "Bd1TQf4h382vOIaPR3jM2R99rwtibKoNGvn3mJSSAyo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.wasm", "FileLength": 7046, "LastWriteTime": "2025-07-12T08:24:05.2012722+00:00"}, "MeJpUq/OiG8LURYDoB1Xwm1Z7EXXe3ZwPqfQK+XGLxU=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\y6vhavdfvm-phrc4jhttw.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.FileSystem#[.{fingerprint=phrc4jhttw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rojoeciz5t", "Integrity": "q9fyOwRKOC9QJfY4IDT4uvAYkkO2NMueygK788ACPEA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.wasm", "FileLength": 1982, "LastWriteTime": "2025-07-12T08:24:05.2022708+00:00"}, "5zD+pDpNyeH2nkrMMyL8Rag60tUNtcDPubN7pAbnXD8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\vcewipgea1-odllsikviu.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.ZipFile#[.{fingerprint=odllsikviu}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4zi909xrgr", "Integrity": "UFykutoRalkt42KbosoPJyMbtA9/IOJ7HwjzS4b6f4E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.wasm", "FileLength": 12718, "LastWriteTime": "2025-07-12T08:24:05.2032711+00:00"}, "r8KOmINgQn2vSnh+NZSwdhr2A44KfgaKEltHC50vosY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\gkbv9w6w75-em8v7c7339.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression#[.{fingerprint=em8v7c7339}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n65fffo5wh", "Integrity": "AW3Q5+zaBsLOSFteolDj/v6mqq6h8oWQjl8Wmd5YSCM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.wasm", "FileLength": 43809, "LastWriteTime": "2025-07-12T08:24:05.2147847+00:00"}, "iQl3U/Nt5q6cy7YercP4k9p3DXI7yO4Dt+mJIc5GAN8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\8wn6npkl14-9er38h0om3.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.AccessControl#[.{fingerprint=9er38h0om3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlancx7970", "Integrity": "dfWjbRhgi4ZBTrpnZOA5EZCrYXu2GYp33Xqy+XmXh8I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.wasm", "FileLength": 8606, "LastWriteTime": "2025-07-12T08:24:05.215785+00:00"}, "Gl/HUnMSLJKcMAgX9A2mBsRLceD1/OkrujGvaUalVg0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\y976jf80c0-5n4il3qn29.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.DriveInfo#[.{fingerprint=5n4il3qn29}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wk08zacyg4", "Integrity": "8F/KcasSGB4S1b8gIS4/WHIGWStyS6vN2j5w8wXbJ5o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.wasm", "FileLength": 6069, "LastWriteTime": "2025-07-12T08:24:05.2167855+00:00"}, "9hEpr8Ffu7X6Npsk9S2B7cg8sTONxIHz55cpct0+/UE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\deeczki5gz-q19vnktpak.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Primitives#[.{fingerprint=q19vnktpak}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hcl20q90o2", "Integrity": "b++Raa/3GRPTMAemOfRryhk5ait728noAZNXfOGRmEU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.wasm", "FileLength": 2170, "LastWriteTime": "2025-07-12T08:24:05.217785+00:00"}, "elS+cCoc2PwiCkOnY4KltLUHN5rOhXAJWYpKYAD7Nzw=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\wau2f65bop-m01xezp84e.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Watcher#[.{fingerprint=m01xezp84e}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5hwco04ju3", "Integrity": "l7gqqdD1XmOvVgNd4dGG0d1+sVS7IKx9cZzNb1rSzY0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.wasm", "FileLength": 8901, "LastWriteTime": "2025-07-12T08:24:05.3245453+00:00"}, "Dt9wgtInuttrMMTcHBSm2Bk2NLUuHhGzjf0FUr/DCmM=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\16v1f74n65-canbiw5jz4.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem#[.{fingerprint=canbiw5jz4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w0ce466m0t", "Integrity": "oI93ROVDQlzPrdPOvfFbUVxdq/TdFmDecOOGAYgOdCI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.wasm", "FileLength": 2297, "LastWriteTime": "2025-07-12T08:24:05.3255523+00:00"}, "42ck61dpCzPoBpI3HPhb0TsYzZJcKJgMDxvcc9BiyfU=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\rkqdq6xlwz-ovh8t119yi.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.IsolatedStorage#[.{fingerprint=ovh8t119yi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dp3olazm9p", "Integrity": "wXV7CMQlGsZOq/yI6mSMkiHDmBgheAgdwNTJty4YWUg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.wasm", "FileLength": 9513, "LastWriteTime": "2025-07-12T08:24:05.2763655+00:00"}, "+5x8Ms1Ez3dGxn/SbK4VMqefRCWEk6C6ajHNoACVF/4=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\bmgpfiph3u-wjnda4frws.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.MemoryMappedFiles#[.{fingerprint=wjnda4frws}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fja97s81rw", "Integrity": "m7QlOu/McWJ9OHr3Jene9qIqqUDNYNMvZNfIwphXUM8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.wasm", "FileLength": 16992, "LastWriteTime": "2025-07-12T08:24:04.9762209+00:00"}, "8z9QRxLK07/DMATdc7gFpJHU3V5x+syXoK0A6tUUs5o=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\iywp83yfou-7ro2a7frhf.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipelines#[.{fingerprint=7ro2a7frhf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipelines.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1m08d65x4z", "Integrity": "GUpUe9FOVtVgMMlZeIysiPvr6C9/hi4H7BONrTe2peI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipelines.wasm", "FileLength": 31017, "LastWriteTime": "2025-07-12T08:24:04.9802201+00:00"}, "zFeZRCQThsHIl12FHSKRrknGQ+2b0Bk+r0CkZ0c84qs=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\nw78774km4-9pomna8szr.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes.AccessControl#[.{fingerprint=9pomna8szr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hzdxq9coho", "Integrity": "81yCnq7/SH/1Yj/U4w5dddIB98+0RPDv4nC7GxaL2b0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.wasm", "FileLength": 5646, "LastWriteTime": "2025-07-12T08:24:04.9862196+00:00"}, "/JEQkkddLeXERCU/fspHaHEbX21CREPEL3QbJJ+/ULU=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\j00v60mvnz-tqouqgzazg.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes#[.{fingerprint=tqouqgzazg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "whidliziz2", "Integrity": "5As+bJFS+z1W+xD0UGaM1NMZ3vSgg+Kd53lTwNALCP4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.wasm", "FileLength": 11588, "LastWriteTime": "2025-07-12T08:24:05.0092277+00:00"}, "dx0eKLBTTjZ+CV/19vMRa+IYn6vCW5z+zBVx7JtZ68c=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\wh4gbtktjq-2qs4v41wjf.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.UnmanagedMemoryStream#[.{fingerprint=2qs4v41wjf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ywq0n6o836", "Integrity": "vqj7XpFXOp+EmMjVMyNYKii/uLNXql+YsWRNXhYhKkA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.wasm", "FileLength": 2200, "LastWriteTime": "2025-07-12T08:24:05.0337403+00:00"}, "9UxGsSybeYzuLLPkzts9W9aqSa7ss/j0ZQOOdGw+XMk=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\surevs4sks-6m5851g67n.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO#[.{fingerprint=6m5851g67n}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9wkpaji8xh", "Integrity": "CcxihuD84qx5WVldbmzPcajqSR6kcdmOOvZgejVEVtQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.wasm", "FileLength": 2248, "LastWriteTime": "2025-07-12T08:24:05.0347407+00:00"}, "LKrVmOTSv9te+7XKV7At9UAqhF1f9VMH54N0zDsmJQE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\lsg9zi6no4-ejydgoj0oc.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Expressions#[.{fingerprint=ejydgoj0oc}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Expressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r8mdv10o6p", "Integrity": "JStxU+YJ9OCM5aZP6om3XoEHOW4TIIIuDWtdniE/mSU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Expressions.wasm", "FileLength": 217767, "LastWriteTime": "2025-07-12T08:24:05.0517397+00:00"}, "hcTUDHiFtrS9yGAbt7u9NQ1olN+LfVhlfTavoVHVJFg=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\a9abzkcdx9-is9nca195d.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Parallel#[.{fingerprint=is9nca195d}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5zr57svufs", "Integrity": "4eO+DE/5v/EilNlRyQu+tZ5AZXoWa8iW7KDNZ0EEaNQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Parallel.wasm", "FileLength": 88007, "LastWriteTime": "2025-07-12T08:24:05.0847424+00:00"}, "C4Cvlwq2S4zcMN7y/rassPV2ynp5AwlT3qBRBKBemH4=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\dxj8sopbu8-8vjofh88qa.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Queryable#[.{fingerprint=8vjofh88qa}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Queryable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trepdz8lmd", "Integrity": "CXAB0adlvHEmuWLPNK10nO0YmybxwsePqOP4BLNJPsc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Queryable.wasm", "FileLength": 21320, "LastWriteTime": "2025-07-12T08:24:05.1132631+00:00"}, "g/HwzjmxsSBDuUDkyYPmBM/o/9fTIMyB/QasIoCJ9/A=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\pqwnomry9j-zqotd0pp5c.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq#[.{fingerprint=zqotd0pp5c}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "819zd6cnti", "Integrity": "hGK4Vn2RrOfO7zndets9CFX0H2G6b1uG19ElQM8A0N4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.wasm", "FileLength": 56610, "LastWriteTime": "2025-07-12T08:24:05.1182646+00:00"}, "D/i5ZomvLM4p73WqwoJL1Mh4Y6Qe7f2W5afyK51F0Ww=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\9evtq4hxh8-mr8xev4a41.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Memory#[.{fingerprint=mr8xev4a41}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Memory.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "79rwsiw025", "Integrity": "BYoDfONCogjjcnJvbH/Vqyiq4IMElLIUjhgPfxUJayI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Memory.wasm", "FileLength": 21085, "LastWriteTime": "2025-07-12T08:24:05.1212629+00:00"}, "PYGxjaii9eriGDpddBE4JSITSz6TB1cc7GjFlTn+fWg=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\b6xbmu47ir-fsk5apfw1y.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http.Json#[.{fingerprint=fsk5apfw1y}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bq296k6m5o", "Integrity": "E+w9Sz1YnqRGHsmKasMWiqlbCFUsPucShOp2xqDffLw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.Json.wasm", "FileLength": 19910, "LastWriteTime": "2025-07-12T08:24:05.1292625+00:00"}, "zsjKSVWY7EDMAQCqSDbhYfCs19aV99q/H90P3HB9gYo=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\mef8f0t7f9-jzkfjdda15.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http#[.{fingerprint=jzkfjdda15}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4rhmvu43r9", "Integrity": "YLofC+uAE08Ux5v18oCOY8XfnXXoqRXHucCcx7i4LMs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.wasm", "FileLength": 115972, "LastWriteTime": "2025-07-12T08:24:05.217785+00:00"}, "gHoEEHoUui8dtCCen9SIeXkQLeLsXi/0cNhyqjxvvP0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\hvutxsykmk-xweq5xme4y.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.HttpListener#[.{fingerprint=xweq5xme4y}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.HttpListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nf65glf5aq", "Integrity": "02OIcQJsJVJmXmgWl/FPtnYywfVMUZvtojaI4uyRiMI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.HttpListener.wasm", "FileLength": 16311, "LastWriteTime": "2025-07-12T08:24:05.2317846+00:00"}, "F3emAak9HLzye2NgRXeIcdzWkM1GnhPLnlmj2D6RLSo=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\4iq4q3a92v-exa7sc8li4.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Mail#[.{fingerprint=exa7sc8li4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Mail.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7tlqlc2vzl", "Integrity": "67Wbfi7k859Q3kfHJLDRK9eaOfpedVL4LGk4457ag1k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Mail.wasm", "FileLength": 42449, "LastWriteTime": "2025-07-12T08:24:05.2367844+00:00"}, "G8UXGy2aVHUT0/0dNTdr2SAwX22tTM4Zjs8HKDszz6s=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\q7ihexpakp-7nsh0ffju4.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NameResolution#[.{fingerprint=7nsh0ffju4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NameResolution.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8pvcf6hv4o", "Integrity": "fEKnPcKXcU0qZPwouGLtsTvLv/6jcXO85yy4pFGqq6E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NameResolution.wasm", "FileLength": 5993, "LastWriteTime": "2025-07-12T08:24:05.2427849+00:00"}, "hvvFpoVVQ7XFQlMu1PD2f0gufUqVxiZbqLLdGzSDaZE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\3bfal1rf2h-16618q39eu.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NetworkInformation#[.{fingerprint=16618q39eu}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NetworkInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rpfrlvl5kd", "Integrity": "TykzPm72WKIZfkZz7puqEOMR4kKfstaqaGNWb57NHgA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NetworkInformation.wasm", "FileLength": 13038, "LastWriteTime": "2025-07-12T08:24:05.2437844+00:00"}, "CbLmUzIKs/iIv8RDa8V1fVgX6MD/mC60NjqHkfKCGLE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\k4anq8e59c-73n1msm1kf.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Ping#[.{fingerprint=73n1msm1kf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Ping.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k6z0lauc98", "Integrity": "HC+9MBmff+hfTts74jgAidmAySmNHbnnbuTR0lEVEAo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Ping.wasm", "FileLength": 7668, "LastWriteTime": "2025-07-12T08:24:05.2447847+00:00"}, "nukJQR8YwnYiBiPSBRjrbvFeL/MV6VX57Ik6eAWihQc=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\ppjyxiuqw0-cp7icbjoye.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Primitives#[.{fingerprint=cp7icbjoye}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fhq9z9nk81", "Integrity": "UyX7E103tn9fCbTI+zBosG8Iu0AmjAeBCqJWyU7Y4ZA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Primitives.wasm", "FileLength": 46566, "LastWriteTime": "2025-07-12T08:24:05.2487855+00:00"}, "u/jbeYR5/cLsC3ykZhTnXyvHwMJWBgvgG/oYWlUEAKI=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\abee0zad1x-5ac51dyvls.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Quic#[.{fingerprint=5ac51dyvls}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Quic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3giiwpk9sp", "Integrity": "HS4HpXvM8x7niFWoJ77qeNuoetHNYjfJv+JGvJnZMw8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Quic.wasm", "FileLength": 11095, "LastWriteTime": "2025-07-12T08:24:05.2507845+00:00"}, "nS2EbjqjMlCr38kuZb2SnHtNezTYjn2+zEgmSIh+dXw=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\nx9wzuyk53-74urp2owxv.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Requests#[.{fingerprint=74urp2owxv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Requests.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cwdctrhh92", "Integrity": "pFUNOGqTL7naBFyqYZFpVYgZ2iC3mxfbMp7WdKf1vXQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Requests.wasm", "FileLength": 20751, "LastWriteTime": "2025-07-12T08:24:05.2517842+00:00"}, "IZR+NZhmKhncNLqbEWQe+V9kzIwohX687k/zcqwk7Z0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\fxys4ng5y6-q4em7fcmos.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Security#[.{fingerprint=q4em7fcmos}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bty1n81i48", "Integrity": "gXKI7nfI2h+Ppr19L8n7Sl0w0PY2md/+dPs0LHkDpg8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Security.wasm", "FileLength": 33462, "LastWriteTime": "2025-07-12T08:24:05.2558233+00:00"}, "p8uupBMdk76UHb/Q6k11bkCHM4l8jdApCcv/NSt3RkM=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\hdzs7pkfrx-60vubhwq2d.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.ServicePoint#[.{fingerprint=60vubhwq2d}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.ServicePoint.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4s9kt2xgb8", "Integrity": "hANrp18roqpY8f0KsgSUHXT2vzV59O4w53V6leaVrnw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.ServicePoint.wasm", "FileLength": 2169, "LastWriteTime": "2025-07-12T08:24:05.2567858+00:00"}, "IxxdJGFAu1JAIU7MLnil7Su40brxx7LqwKCRcFLspk4=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\eat3jfstut-6qp5a9tfvh.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Sockets#[.{fingerprint=6qp5a9tfvh}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Sockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yooe6hjo0b", "Integrity": "frGWc9I8JsCoqBjykXxirsW+rpKLYFf/WU0GGOWT4DE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Sockets.wasm", "FileLength": 23479, "LastWriteTime": "2025-07-12T08:24:05.2587841+00:00"}, "AyGxBDDw/4PahT58QanC5w3CWDoeYhbaR/lpt48+mAY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\w3rk4m7gz7-kwo41wq289.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebClient#[.{fingerprint=kwo41wq289}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebClient.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "frim2dakz9", "Integrity": "hcEHpc4BsPbmCyLAGWAXeQHmP7LCB40YiWjmoVlylSg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebClient.wasm", "FileLength": 14626, "LastWriteTime": "2025-07-12T08:24:05.2597848+00:00"}, "U1y1O6ZMifDU9MtMXuPDH9nQlRyoUambmJiXYLyx5yk=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\e2erjaf3qg-t1y05ppbtx.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebHeaderCollection#[.{fingerprint=t1y05ppbtx}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "54ztvvfryz", "Integrity": "BWJqx/4okFJhvsdC2nRnsPsprIiwwywsuWLhmwcCQ0s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.wasm", "FileLength": 10375, "LastWriteTime": "2025-07-12T08:24:05.260784+00:00"}, "E6xxhN5ddKE+TQ9EBJiZiLRTuTswWYxEH8GAulAamlk=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\tr12rpxil3-232idirb7b.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebProxy#[.{fingerprint=232idirb7b}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d84uqjzx4r", "Integrity": "E5mQSm54bsDjWp5u8UA/InuqT2saCBU6VwBeINK/uyk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebProxy.wasm", "FileLength": 5667, "LastWriteTime": "2025-07-12T08:24:05.2617845+00:00"}, "Tizc5zN7UcJUm9tPOjOqEI9VzAFej4l614PmfNWecz8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\27i2vbmux2-1rwz2j0ytm.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets.Client#[.{fingerprint=1rwz2j0ytm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7067r3xlzo", "Integrity": "J1G4APD9A9YrpZtOyG1Ra3SftJJm2Qx0MpnJ2TfGpfg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.wasm", "FileLength": 17399, "LastWriteTime": "2025-07-12T08:24:05.2637905+00:00"}, "8huYt77cDUB5bxbiAMErVMK0fxRtcNqUDFJ/d7NXo4M=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\sf2w05zoo1-7ns4dakcmj.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets#[.{fingerprint=7ns4dakcmj}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "89f7t4dqmn", "Integrity": "BJpgWRcAbp+xrjLu7q4WDFzFpMcfW9z+1Mgben3GwEA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.wasm", "FileLength": 39056, "LastWriteTime": "2025-07-12T08:24:05.3149949+00:00"}, "QB2+4oApMSnYCBlrQZHUY5mdu22R8UjdzOf2jAAbOc8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\tb88j6f588-hyl81vktsx.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net#[.{fingerprint=hyl81vktsx}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "byukzbr3ri", "Integrity": "s1iZ+hIO38BqTBvBM7tJxkbd4e5RzuherNjppCzkWU0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.wasm", "FileLength": 2756, "LastWriteTime": "2025-07-12T08:24:05.3170411+00:00"}, "1VA7o1hK9qmZaymRQwQczDpb1nfQxKX+R0gLxgpqIJU=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\eij4kq473a-3ulov9gswu.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics.Vectors#[.{fingerprint=3ulov9gswu}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.Vectors.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pqb5vl6kko", "Integrity": "sR2J0CLvti5NE3I0sJWp8mRkvo/h8sUYCQMXk6oDJ98=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.Vectors.wasm", "FileLength": 2265, "LastWriteTime": "2025-07-12T08:24:05.318004+00:00"}, "kS1QR/dcZ3B/vTcItg9D5M+mO3ylzMdMxAX74YjN93w=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\3e1z5dlqut-3xrgeyndxe.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics#[.{fingerprint=3xrgeyndxe}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6s<PERSON><PERSON><PERSON><PERSON>", "Integrity": "BhtIimBTaAkFUD+k+CIJ4UnnoGpnYHyhksIB3L3C/rY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.wasm", "FileLength": 2025, "LastWriteTime": "2025-07-12T08:24:05.2768714+00:00"}, "e6IYSG70XAJ5xn/qIO4m+kMTbqs40XC3/dIA7QB3v3U=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\jup49mqhna-2onw4a3huo.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ObjectModel#[.{fingerprint=2onw4a3huo}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ObjectModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sxvoqv12vx", "Integrity": "sH0U8F9JMXRS3qbk9bl3ccwhYBo89vdsF6lobimEwro=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ObjectModel.wasm", "FileLength": 13597, "LastWriteTime": "2025-07-12T08:24:04.9762209+00:00"}, "QA9KKLhyMdQu/yNNmuUDeqzhAAANjRpkb+uY0G5sXSE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\gsyggzcz69-viwejdyf0o.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.DataContractSerialization#[.{fingerprint=viwejdyf0o}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v0tpe5zuap", "Integrity": "0e6IPfEPIUv/S2HWclJlZOUaWE3dv6nvfppe0WbUsIs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.wasm", "FileLength": 304582, "LastWriteTime": "2025-07-12T08:24:05.0022274+00:00"}, "sxnSH5j4R0GtQity4O/8FTsWQn4UjRupXvMbrbYeQCQ=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\6mnfbq5csp-gccfg575xd.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Uri#[.{fingerprint=gccfg575xd}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Uri.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "apk4l8jhbp", "Integrity": "MvmtBja82bSzObdnObl32oWRxbN4PmU44J1flF4g8Ig=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Uri.wasm", "FileLength": 42187, "LastWriteTime": "2025-07-12T08:24:05.0127402+00:00"}, "fPNZHgBM4e4n15qou2X5GeV+sxWHiX+UrIZoLjsCXIA=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\1lemy8eeu5-hubb8bdrav.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml.Linq#[.{fingerprint=hubb8bdrav}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "49q40n9g8j", "Integrity": "6KnXwcHhS/F7ingX74oTAU8I5+sMgbt7Y5U26NrIamg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.Linq.wasm", "FileLength": 59684, "LastWriteTime": "2025-07-12T08:24:05.0387412+00:00"}, "OITyE5kJ6PIqrMnxX2xorrHA/7bfcpNDtYq6ZDL1f2U=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\dst121ejfq-36oo928nq7.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml#[.{fingerprint=36oo928nq7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5219u0a8mx", "Integrity": "nevJgTU5ugwx45kJYmr3G7BEcyLdbfeb17XMGSK8EWk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.wasm", "FileLength": 1069740, "LastWriteTime": "2025-07-12T08:24:05.3139951+00:00"}, "QYsoagocwc1zi3dxn51M98EHZD86UAO+EbmQVf2aqsM=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\b5bx9301gl-ytnrm7l6ga.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.DispatchProxy#[.{fingerprint=ytnrm7l6ga}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aduvzikrr4", "Integrity": "fjbAERry1JYO91FDWwM1IdfWhm7g2AyRHUbjbft6l90=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.wasm", "FileLength": 13142, "LastWriteTime": "2025-07-12T08:24:05.3160015+00:00"}, "QwDM0ytHCeXb8PqMKsSgQ7GDl4gDMtqpQuJxbGESOpg=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\h0aa8rsp9k-n2z595t7zv.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.ILGeneration#[.{fingerprint=n2z595t7zv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xfa4ownoqt", "Integrity": "3RWNNkoqTXtUctZ3ku3YNWFEEhn7i8CSXdyzrgIDiWY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.wasm", "FileLength": 2267, "LastWriteTime": "2025-07-12T08:24:05.3160015+00:00"}, "cZSTTaHyEfzWQnYdCLDESktK+PC7EtnYntKEWcxIWJk=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\w2u3e6wslx-icwj787vcb.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.Lightweight#[.{fingerprint=icwj787vcb}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hf748uts2z", "Integrity": "nQ4hHq7boWMq6GidqZyVJzUVsrygcGXF/LjVdAzwfKs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.wasm", "FileLength": 2224, "LastWriteTime": "2025-07-12T08:24:05.2768714+00:00"}, "8WYuETrxCcbvVCwRsfz1rbRDFTtkMh6s13eMagNj7ks=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\uytsmwsnks-w74i2eyu03.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit#[.{fingerprint=w74i2eyu03}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f2dyoz0440", "Integrity": "WQW/EaQHfIOJ9JqHDzmmpfqVxWoum6C4g/Ptohu5CWw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.wasm", "FileLength": 52814, "LastWriteTime": "2025-07-12T08:24:05.2808786+00:00"}, "m0WMKl0yB8cbtaDaTf7OcsGBYbb4j5hnizxApTdTbpU=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\nrl3ud4t22-5iae6nghwr.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Extensions#[.{fingerprint=5iae6nghwr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r9anno5xjk", "Integrity": "QQVuz+kHRyv3ihrKq3AYd8Jny+Yc2OTngPSWs/tIr6U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Extensions.wasm", "FileLength": 2146, "LastWriteTime": "2025-07-12T08:24:05.2818791+00:00"}, "6ys3MRPSc7ffidNZgVszF8IDol73+Xc4FU7aQ0Yyd9g=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\jypfti31p9-eu6g5dmyrp.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Metadata#[.{fingerprint=eu6g5dmyrp}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6z0rgr7k1b", "Integrity": "sgmwM22TOz1EsW7J9RqeFRNIuic0wSMA3eCiHw+95IQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Metadata.wasm", "FileLength": 195561, "LastWriteTime": "2025-07-12T08:24:05.2959653+00:00"}, "rmiCwtyShMwtOZePt+KYHL9tV+cnet2MKJgisU1tDuA=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\nfsuti8x3k-8h25o84kf1.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Primitives#[.{fingerprint=8h25o84kf1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mrcrf36cet", "Integrity": "YjR7t0ASlrZqV++UXHgCpp+PFOUC22LYujJQJ9rLe+Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Primitives.wasm", "FileLength": 2363, "LastWriteTime": "2025-07-12T08:24:05.2969625+00:00"}, "Zpqm64VdOs4ZvEMRUMIe4b1o3vAzqjO9QgmYG7QuNnY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\a98tu6u8gw-ncossl2nra.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.TypeExtensions#[.{fingerprint=ncossl2nra}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "renrjgtpjv", "Integrity": "Q/L8FeRlM8Q2kV5vxU0KYRwieUWU8b/DeMkjBxZz6Gw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.wasm", "FileLength": 5728, "LastWriteTime": "2025-07-12T08:24:05.2979638+00:00"}, "zTNNLZpuBXRNHaqOINpXTIUJfxadb7z/aTExPZAyNKI=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\oispjiuvjf-fu4huwo1gu.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection#[.{fingerprint=fu4huwo1gu}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dkexmxjpzl", "Integrity": "/TF0XNR6/B5plFUZa09azq7vNsemEiVAxONcxSGv7mI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.wasm", "FileLength": 2453, "LastWriteTime": "2025-07-12T08:24:05.2979638+00:00"}, "GzXjMReQNdtYwJPdTE3jIXhwjbksc0LfpA2FDhCzHig=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\lujkp03kvb-ckle7drwww.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Reader#[.{fingerprint=ckle7drwww}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Reader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kf59tlsfkm", "Integrity": "wLRkZuFfbhZ6iMhaB+igJQCHyWiHh71HE0T/0B3ghVs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Reader.wasm", "FileLength": 2112, "LastWriteTime": "2025-07-12T08:24:05.2989638+00:00"}, "jQXLsIojsBpDbF2X6+z8253+1WGIvgC6WDULye9MpB0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\mgj0jmd2dp-n9sh7wxvjn.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.ResourceManager#[.{fingerprint=n9sh7wxvjn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.ResourceManager.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3u6ji2kjcg", "Integrity": "pbw34r0coSL7Tbr3MxlCbMAFPqdFXxd///rwFcZN1Os=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.ResourceManager.wasm", "FileLength": 2232, "LastWriteTime": "2025-07-12T08:24:05.2989638+00:00"}, "/iCI5DBJf0ePVGewOvwSSx4Eo7TcL2pwTq/Mn3FM96Y=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\pibjlyprtq-9kebxuk0er.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Writer#[.{fingerprint=9kebxuk0er}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Writer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w09afeatn4", "Integrity": "MJP3uYVxDtNWdi7aBeYK5bIe+OS5BsXdXp34RRXTKCY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Writer.wasm", "FileLength": 7743, "LastWriteTime": "2025-07-12T08:24:05.2999638+00:00"}, "u9UjVuoruKwmasCBhfOWcNeM6zB7jLwo6tM+D/rnLEI=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\3r6fqzap7k-j5tmcq1bfo.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.Unsafe#[.{fingerprint=j5tmcq1bfo}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "16l2yr4gcm", "Integrity": "VzpTo+RGtJL7FmUkG6n74NCxlrH6GYRUk6QRN1+JXaM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.wasm", "FileLength": 2120, "LastWriteTime": "2025-07-12T08:24:05.318004+00:00"}, "xrlk6UD8ozYZjAO5IjxhaHo0YhaZ0lUBnFGgGgIo6ek=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\1sby0ayqn3-02zc66007n.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.VisualC#[.{fingerprint=02zc66007n}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gdr7lgvqz6", "Integrity": "rJTVgTFol1iMKqhcYSiwvUbigJMwqrt8Uvn8NdAgV8c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.wasm", "FileLength": 3068, "LastWriteTime": "2025-07-12T08:24:05.3190026+00:00"}, "lmlgyuwmRjq/hKd/Yc+2WzN0PtjKcTOnCNABWz8U06w=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\6fmfs6337m-oiot8mv0g3.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Extensions#[.{fingerprint=oiot8mv0g3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vfrd19jmim", "Integrity": "1R7rQrXvE8Hkr10pcoPZRdkFtUFCY6OYuG9lJNMe194=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Extensions.wasm", "FileLength": 2993, "LastWriteTime": "2025-07-12T08:24:05.3190026+00:00"}, "/Vtr5wY8ThY3R4WKj3IwJVR5nOt9iEUiDuV7TJDbxb4=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\swmceaywei-vvyys879d4.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Handles#[.{fingerprint=vvyys879d4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Handles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ny55yyw8do", "Integrity": "b9d1is02yhlOfwSiOPD7MJh5ry//CnIW7OZCAcnQrLE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Handles.wasm", "FileLength": 2193, "LastWriteTime": "2025-07-12T08:24:05.3200006+00:00"}, "tMlFV2F/GPvcrq5OWep/heczmhl4fAR/HBkT6OH37kA=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\nh5cafe76c-y6arj9x08o.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.JavaScript#[.{fingerprint=y6arj9x08o}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lqxn0cbh2v", "Integrity": "jarmqiUKR11EATbNwiegrZY5pM+d9+TNU2zVX2LF7hE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.wasm", "FileLength": 31682, "LastWriteTime": "2025-07-12T08:24:05.322002+00:00"}, "+BFBh0C/CXaF7IyX3vTXCS2TXT1A6hnhpm5/bLbdfv8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\978uqlud0j-4gcavmlio6.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.RuntimeInformation#[.{fingerprint=4gcavmlio6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vxe4lavh0s", "Integrity": "XQ7ju0HCdfD25K9qzAm/uE5UAWp5kyQkFiZU0H1dh/g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.wasm", "FileLength": 2139, "LastWriteTime": "2025-07-12T08:24:05.3235378+00:00"}, "XvItUHggRp9ZFsg0N0Pz87gzoZaXuaF5qdlRxw8yCHQ=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\6voj337tea-zcf5xh4j89.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices#[.{fingerprint=zcf5xh4j89}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dbgyr4mbib", "Integrity": "581ClXqiUPWr97KQQ0lh9ntTx2mkun5vFuFdPYaHVVc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.wasm", "FileLength": 23801, "LastWriteTime": "2025-07-12T08:24:05.2788803+00:00"}, "/AQQp189eFMlM7mlFTpjuMgSiPyHBJaG3JzBXHaWDfQ=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\9egvo60cp0-unx94uxfba.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Intrinsics#[.{fingerprint=unx94uxfba}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n0251x81jn", "Integrity": "dx4Sr4uCJgUsdKqQKbRIkKWyHe/vZjWKCN0+0igoBJo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.wasm", "FileLength": 2736, "LastWriteTime": "2025-07-12T08:24:05.27988+00:00"}, "yQBbemtGqDrXjtdmD9MGIADMHfdgwghWAGPkgku6QL8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\1337zsovqi-lio72255su.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Loader#[.{fingerprint=lio72255su}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Loader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "111l0cewzo", "Integrity": "dYh8wh7QK+EKoi/82oqhNjQ95nk7Kb8ACyxqoRb4uAk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Loader.wasm", "FileLength": 2317, "LastWriteTime": "2025-07-12T08:24:05.2808786+00:00"}, "aIp9oIwwP7DBEPv5jhWSLDo2nmp2qAToQlz5qWYwCLY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\q7m657m48q-drjgq7o7pq.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Numerics#[.{fingerprint=drjgq7o7pq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zs8dj8t5t5", "Integrity": "LW99X1O3TXmAM1p1CAG44lIJhkBUo95HIgKsrCFT398=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Numerics.wasm", "FileLength": 53377, "LastWriteTime": "2025-07-12T08:24:05.2854396+00:00"}, "aET7If4VDufnJj9Le/ctpYzJjRDgQ9EF5zL09whO014=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\by0ogzs2j1-7hfaupc75z.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Formatters#[.{fingerprint=7hfaupc75z}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6s4tktbxu3", "Integrity": "dtWvYqMnxFg15AnyzATN8x5fJMgr8Z79xcCuov9p4x0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.wasm", "FileLength": 24579, "LastWriteTime": "2025-07-12T08:24:05.2874396+00:00"}, "QFjdNmxRAF9vnawFVJtrVI2AT6ZyfP6hVdkEUls2cyE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\9fn80ruqbw-avkizgk9x1.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Json#[.{fingerprint=avkizgk9x1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7dru20eg6o", "Integrity": "9fcJiQN/IPC1NFVQJINqPScXWKxfhb6L+VJv9FBZLnM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.wasm", "FileLength": 2243, "LastWriteTime": "2025-07-12T08:24:05.288439+00:00"}, "XZY1pnNRxI3XjjdtcOKv/0sN8othnApo+pkveB2cyTs=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\c15hitipx8-k309oqfoa4.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Primitives#[.{fingerprint=k309oqfoa4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6v1575zl41", "Integrity": "TlyxoZ1KrArE0NkxJPxz2fSn7TxwJDGghwwacSF7aj0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.wasm", "FileLength": 5496, "LastWriteTime": "2025-07-12T08:24:05.288439+00:00"}, "DeeLF+e3tYMKrA9rENMo7fQTA5znBg1sEjuJnv3Iw1Q=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\oh42i1ti6w-0jwn7dp9nd.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Xml#[.{fingerprint=0jwn7dp9nd}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yz2xe2blvx", "Integrity": "kmyYm5klRjvUx0qoAfPJY5Ae3AkzzlWKHMURwCfy7yk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.wasm", "FileLength": 2561, "LastWriteTime": "2025-07-12T08:24:05.2894404+00:00"}, "fdEsGT0uhbvd8ed/zkhb9424MJqGlqLS+k9+BiBIr3Y=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\5e6llqp1ef-z7sglijpt0.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization#[.{fingerprint=z7sglijpt0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "udkn1zj0w7", "Integrity": "DvwWXdG13Y6p9ZFvrLSkohAszmliFA4jHSElfDmFMv4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.wasm", "FileLength": 2497, "LastWriteTime": "2025-07-12T08:24:05.2904408+00:00"}, "PF9Ks0ppUJLXRirs5quTZn8Q6amXighroSYP+LaN9QY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\9mvq5z4m9v-c61u9af934.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime#[.{fingerprint=c61u9af934}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a5pokwbxou", "Integrity": "TpmyQYKOvXIyrQcAzHQu79M4iG3seS4BEIeeD18wMm0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.wasm", "FileLength": 10722, "LastWriteTime": "2025-07-12T08:24:04.9752351+00:00"}, "oTXwMMaU2R9uVh0mrEBgwcO6/39yUsub9qGlFx/t1NI=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\8kpdrbw2d4-rl384mt7e7.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.AccessControl#[.{fingerprint=rl384mt7e7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bfwh06wbmi", "Integrity": "Z1a5PJL9ixZ5QeudaRoK2aoZxr12cw8sKhDyHIIhFPw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.AccessControl.wasm", "FileLength": 17218, "LastWriteTime": "2025-07-12T08:24:04.9802201+00:00"}, "nWfgT6lGHFkf7E8BGe92k7Vna6FrbcUOWlAIHRb5/bQ=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\fvqtws819x-845vwhbngt.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Claims#[.{fingerprint=845vwhbngt}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Claims.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "650y71lryt", "Integrity": "OlAZBERUyOQhNpFlkXLN/Ljuoi1SqQmC/nFMUeecaoo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Claims.wasm", "FileLength": 16443, "LastWriteTime": "2025-07-12T08:24:04.9842241+00:00"}, "CsUxRL2RcxJej0y77NQ1YYSAPC/cdUxgovzjA4zbT0c=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\0lophduduh-v13868m0ek.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Algorithms#[.{fingerprint=v13868m0ek}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h61zxljlmj", "Integrity": "R2reDiWUHDvBgOmxrzbblzQKuiVoNcGVyTDcA+u2Os4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.wasm", "FileLength": 2715, "LastWriteTime": "2025-07-12T08:24:05.0032278+00:00"}, "ffGjHIedGj9q32nFaPDwu/wggW4Cx9+pm06WBaXp0x8=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\0uh6bvmuid-uqhggvgrxh.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Cng#[.{fingerprint=uqhggvgrxh}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "etho4sm9hq", "Integrity": "znvJJczXKdbYbrVUV/Ry+UNhFLMhVhQZQa3lIl25TPM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.wasm", "FileLength": 2474, "LastWriteTime": "2025-07-12T08:24:05.0082265+00:00"}, "gZlp2IwEJiKFXoUXbbDxWiz0xNBf7TG5EcRiB2iwlho=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\z57c7aberi-ujtlhftd9v.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Csp#[.{fingerprint=ujtlhftd9v}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nevvo7wwfh", "Integrity": "QbtqkDn0ONiXxHp//wMMjirW4ttuYjsWvV/jOqoPfCM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.wasm", "FileLength": 2335, "LastWriteTime": "2025-07-12T08:24:05.0092277+00:00"}, "qfWa0kyuYC4TcUpNAuZWDYclF5BFjY8kGf2Bx3EhZIU=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\z17pqpsrpw-9v1z3jix27.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Encoding#[.{fingerprint=9v1z3jix27}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gub9b8e30s", "Integrity": "yLC82OQznBeCQo8Cs0HMotwNU06FKEF9urxXKSu/dnk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.wasm", "FileLength": 2268, "LastWriteTime": "2025-07-12T08:24:05.0092277+00:00"}, "3jSnQK2/Q5x6+ICsPoMMA/azX3fmDxKqyp4ghrywvBI=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\zlazosfzh4-zh8vki7uno.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.OpenSsl#[.{fingerprint=zh8vki7uno}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0wgykgr2d1", "Integrity": "AHK4gUkTaOLrwyznkLTIwKJHf9kwIdCJrV4Ujfq17FU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.wasm", "FileLength": 2205, "LastWriteTime": "2025-07-12T08:24:05.0337403+00:00"}, "CaJTyIzg5I95nLwAbWMXz1KL/1XpUVu4WlOTDCWjMr4=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\as0upcgik9-x3mep84tc4.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Primitives#[.{fingerprint=x3mep84tc4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qidzpu8srf", "Integrity": "MqBnPBjBOg8L59TJLNgp2pffrPAC2MijlS/7wwqM1cA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.wasm", "FileLength": 2330, "LastWriteTime": "2025-07-12T08:24:05.0347407+00:00"}, "Jo3yGKmJpEaEvARmjQML4AqLXqbffubucUe999q5HrQ=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\59cg47r8nj-j8t07tanc6.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.X509Certificates#[.{fingerprint=j8t07tanc6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gfsuzdddst", "Integrity": "bFK+IieQ6EdMf2LZ+FTRNleVdeK9DbFxMGe2GfeH4OQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.wasm", "FileLength": 2669, "LastWriteTime": "2025-07-12T08:24:05.0357402+00:00"}, "EJMHTsg1aK9en+ctDZgYM/SN0mkpj8quoLR6/DaQWaA=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\d3hzn3ck14-05gw4icbhi.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography#[.{fingerprint=05gw4icbhi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kp4q0pwhuu", "Integrity": "qCFJ2KKwfR+8G/usUTOK75/unU5h8OtIjyTY3PvGe00=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.wasm", "FileLength": 192146, "LastWriteTime": "2025-07-12T08:24:05.0637408+00:00"}, "Ahe7SNjeU8s5nR/opOv9bM46B/tKkIzWRu3hpUGzfFo=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\tsl2wlhpy9-m4byz8sabo.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal.Windows#[.{fingerprint=m4byz8sabo}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3g9uo2rj7z", "Integrity": "raTVsC71M4T3mkIUawyL+wHoqc6/NsJ3n8nwDLuTgLQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.Windows.wasm", "FileLength": 11377, "LastWriteTime": "2025-07-12T08:24:05.0777403+00:00"}, "fYu5aeBvfby7mUCcohj2V2rTv/7FdJ/l7L5KpNo60gY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\y1cggbqfuv-5bg4gnsg0x.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal#[.{fingerprint=5bg4gnsg0x}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "frfnd1e7bz", "Integrity": "dd8pRYgDkGqwQJSmHpxnVsotvI7nimOaPILaWyQcbIo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.wasm", "FileLength": 2160, "LastWriteTime": "2025-07-12T08:24:05.0827427+00:00"}, "jvux1lXnUV8/DDdc/jBYij3qe8fPBxoJA77AEgsgwTY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\94gjr8g0x3-fiastri9ki.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.SecureString#[.{fingerprint=fiastri9ki}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.SecureString.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "03w5o7tfgv", "Integrity": "VKoj5gSUlDjFY07jbCzZyn1PuImho74o8m2QelygF9w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.SecureString.wasm", "FileLength": 2188, "LastWriteTime": "2025-07-12T08:24:05.0977488+00:00"}, "74xpXq3+WQgUbDls0QKevkwRrTbD31hc9ibrWjsVWp0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\1jjk5q0otp-80ju9y7l18.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security#[.{fingerprint=80ju9y7l18}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "910uig5nh5", "Integrity": "UwKb8SUGmpiDNDzsqvKoDSyuBjF1U5KaFvk6m0Ikclc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.wasm", "FileLength": 2977, "LastWriteTime": "2025-07-12T08:24:05.1192636+00:00"}, "sh6Sa5vl0slql5r2+xA9snM5fJZ+vpZkyQR8HyNVQ8E=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\xep7m7xiti-tv3f98ejk8.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceModel.Web#[.{fingerprint=tv3f98ejk8}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceModel.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sf69pgmb1s", "Integrity": "KF5VncW8OjGhYiD+Ca7GzpFNIrdqkLIQnulEMxadPGk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceModel.Web.wasm", "FileLength": 2537, "LastWriteTime": "2025-07-12T08:24:05.1252649+00:00"}, "bxh9tT43xDDVNQxQiC+iu/JounyFJZgGBk9m3NNKstE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\q6w3864pvf-j3o0lzzi7t.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceProcess#[.{fingerprint=j3o0lzzi7t}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceProcess.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jc8eymt107", "Integrity": "PVT1xtBwE4/uZLidPJYlilBRELMkVTeKHQOcfhR6Mi8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceProcess.wasm", "FileLength": 2299, "LastWriteTime": "2025-07-12T08:24:05.1362624+00:00"}, "KfiAZf9rYIXti3xIN8O4BcUf6zmlDhXtlQqBn5cvLlU=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\666b9hnxxm-hhvnal3rqz.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.CodePages#[.{fingerprint=hhvnal3rqz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m2kohm1ff4", "Integrity": "nhRDoFu+049ErA6P2TvuBvja1Z/4N5W4XrJfdPZCdeo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.wasm", "FileLength": 518381, "LastWriteTime": "2025-07-12T08:24:05.2778785+00:00"}, "XR8gu0YRAf8a/HQKSwzwzA1J9D5Zxdav9iG5hVYCXrs=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\jpi6lsosig-esy6mc7t8y.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.Extensions#[.{fingerprint=esy6mc7t8y}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9lydlcaahu", "Integrity": "owq+c6AUh4KbWE56QntwUaQyQTrx/7vHIyZUqG8fOhE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.wasm", "FileLength": 2248, "LastWriteTime": "2025-07-12T08:24:05.27988+00:00"}, "8HKQzj9v7RQ8nmqjch2QIbz7FCsUe3apULi4wVVVUPg=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\matgf3l45u-so36gwcdvm.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding#[.{fingerprint=so36gwcdvm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "31v0376del", "Integrity": "F5ydBbOQYZ4a+nS8cDzIbKBGEF6/Ds9Bw4b5Bq6L7iI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.wasm", "FileLength": 2228, "LastWriteTime": "2025-07-12T08:24:05.2808786+00:00"}, "yR3fANt83F0VluaM8/0+OFiEfETNfX8xWNYKB4R6XIc=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\q3bhay0r5e-g33dyw9sdz.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encodings.Web#[.{fingerprint=g33dyw9sdz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encodings.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4gzqmv3rzl", "Integrity": "9Ne1CvP66MY83w0WURyUuvil5Oj0QchKMkD5Opll2U0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encodings.Web.wasm", "FileLength": 23940, "LastWriteTime": "2025-07-12T08:24:05.283423+00:00"}, "jNU6w2jhHoChnAJ4zX0Q69+n+So6PhBaMH6eKNOAY7g=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\ix67ksw9r8-o0tkiahb9x.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Json#[.{fingerprint=o0tkiahb9x}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w8xldhchtb", "Integrity": "cFO+7RIYmu/Tk5SCYZ5zDrTfWKrkXQCLajyCbsGCzlg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Json.wasm", "FileLength": 221078, "LastWriteTime": "2025-07-12T08:24:05.3275541+00:00"}, "zWF0C/3rP2++J3O4+kL/ijUGi/EKesVMFFR704S/HZM=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\kvobbx2l8o-5x7wnnoptp.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.RegularExpressions#[.{fingerprint=5x7wnnoptp}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.RegularExpressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rltf9ilvx1", "Integrity": "YXuTBWH+7p2VCQA+sken9raTdvVqwj2ALpW6+Hoj9iU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.RegularExpressions.wasm", "FileLength": 156838, "LastWriteTime": "2025-07-12T08:24:05.343599+00:00"}, "KrWre7kq0OOjBn4/JVJg9StNsqHUFNSDk60jbhEF8MY=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\r2du4sa0g3-sans7ybw3q.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Channels#[.{fingerprint=sans7ybw3q}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Channels.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0whzwkaod5", "Integrity": "sCqw7oi+jBbq8m/kfAIBBereUtBZKYuVsH2kK4AInnA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Channels.wasm", "FileLength": 20997, "LastWriteTime": "2025-07-12T08:24:05.3465956+00:00"}, "7XDCPpld0YH3trB6ztmgsubjGD+moDRXvMmTj5CEkFk=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\c8woi6wko1-w9h9po1nje.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Overlapped#[.{fingerprint=w9h9po1nje}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Overlapped.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mfk05vrk78", "Integrity": "jud6Xm+goKDplyACNaH96kLlmIBEgGakRwaC9bCOmjw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Overlapped.wasm", "FileLength": 2305, "LastWriteTime": "2025-07-12T08:24:05.3475956+00:00"}, "FFgem0U32Nnku96/0TNvFQNoKuv8aZcihp5cKEbhKV0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\7s4zlw88j6-fhcbb6k32h.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Dataflow#[.{fingerprint=fhcbb6k32h}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fwpn5n37ph", "Integrity": "37OTuJW/m4U0HH5adciUUO+oAfMBCf1wfyhVVVrlvOM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.wasm", "FileLength": 74089, "LastWriteTime": "2025-07-12T08:24:05.3535964+00:00"}, "kKMxQ7tGrAGdjxn2MyFe7dw3xlp/e5DaIlu1hBVKbBc=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\i2c5vte0cx-hs7o6v5tiq.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Extensions#[.{fingerprint=hs7o6v5tiq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zufethjtgx", "Integrity": "y9hFEKzPbaVRGdzpa3fs51L2PgYL7kz69RVAjKifhXU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.wasm", "FileLength": 2298, "LastWriteTime": "2025-07-12T08:24:05.3541033+00:00"}, "nr7PxA5EKy2OBMeOVqhLLrglYnLCY8ltAjsMA8LDUaw=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\gb1jf7hjxa-k8t7rlfiat.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Parallel#[.{fingerprint=k8t7rlfiat}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "21dkb75re7", "Integrity": "9vX4o6tdEL585y9bu78+XkSEyTQKxBi4Wmfxd1oJQ7M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.wasm", "FileLength": 21516, "LastWriteTime": "2025-07-12T08:24:05.3561108+00:00"}, "6hkwdTG4hG47DY/rLGZvpnY/ub5oN7Ql9fQkCk4tP30=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\l0lhk1fyj0-tbqaq378z6.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks#[.{fingerprint=tbqaq378z6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxnjaeocll", "Integrity": "IT4/N3Nj9ZXzTSvbAospKqicBDA1wiLYmlHtHHxk+ss=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.wasm", "FileLength": 2551, "LastWriteTime": "2025-07-12T08:24:05.3571108+00:00"}, "FjqwWS0gmr+CLfySzndXaV8G84lfC7Da0hcczPZMcjE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\wfh4bebd17-76ednmbzmi.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Thread#[.{fingerprint=76ednmbzmi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Thread.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nbh9aky2fj", "Integrity": "83lDK8/Dcp1bEomcfSKxl5MYiqRcq8Ps/lQIghzQCSg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Thread.wasm", "FileLength": 2329, "LastWriteTime": "2025-07-12T08:24:05.3626155+00:00"}, "EKg6goS1lOn+cwqOnwDjx+kDRHO6Kjabkf2BP7X//yA=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\quodrsvu19-dggkxr83to.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.ThreadPool#[.{fingerprint=dggkxr83to}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.ThreadPool.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0uvnflgmym", "Integrity": "6GPlnWI2/ooLqCBWrPQ/gJlSQtYaR+iPGrTu7GQeBwg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.ThreadPool.wasm", "FileLength": 2254, "LastWriteTime": "2025-07-12T08:24:05.3626155+00:00"}, "vO/fqMgn0qEx+627wrslx5NQlEOjXbcPoYnTKGMzJYs=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\vu4nkrr9bj-j8kpz62luj.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Timer#[.{fingerprint=j8kpz62luj}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Timer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lmao32glr2", "Integrity": "M0mdtBlkm57FpDUvcD1SqumYPtnoqTi3Kru5GYqzQP8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Timer.wasm", "FileLength": 2126, "LastWriteTime": "2025-07-12T08:24:05.2904408+00:00"}, "qv8P1lgawrVIQ1sBj2TUC/UYIETC/W0RqYBUNmMJj6U=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\omphzjeel5-myoimpdcs6.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading#[.{fingerprint=myoimpdcs6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hlbkx72ow5", "Integrity": "SyvRr2eLoSS9OCef/k8wQfxnRiCLx+YrHXyT98Kmhc8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.wasm", "FileLength": 14939, "LastWriteTime": "2025-07-12T08:24:04.9782203+00:00"}, "Bk9rp4cW6PqVowSTTgLlA9lmBbMW/XmiksKUZw35Sxc=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\qbokveexsl-7hdcq2g011.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions.Local#[.{fingerprint=7hdcq2g011}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.Local.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8zhceh2jcz", "Integrity": "BHgZMVvLdppCMzTvKrM+Lb3vgkzLmWkwnWhhTV/A2JE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.Local.wasm", "FileLength": 52485, "LastWriteTime": "2025-07-12T08:24:04.9892192+00:00"}, "O5eZHqmAWMDuyH4vYuKW/62UGdjQx/h4ap1S/Ax7BXU=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\gjdub3zeo2-p83alyf85n.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions#[.{fingerprint=p83alyf85n}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oqllczrnyr", "Integrity": "UMljsZuTp31qCUhx/IpRefLT8lg06LS9lORl7XYl0+I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.wasm", "FileLength": 2364, "LastWriteTime": "2025-07-12T08:24:04.9962264+00:00"}, "anpmWgQHJnagRQxiTzkTj8E77Dbd9AfODe4EPq/NJqk=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\76f0vb2qpi-ygaqfhsa0b.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ValueTuple#[.{fingerprint=ygaqfhsa0b}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ValueTuple.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rp26x2c5yy", "Integrity": "waMSz5KdQrTdT9dmvxuFFii7dQqEmwfn/nJGOxzqCMg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ValueTuple.wasm", "FileLength": 2169, "LastWriteTime": "2025-07-12T08:24:04.9972281+00:00"}, "Qf+j7N+c87lEQB/WxhlhDHebENYg3UZImdfzQSyJvdA=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\25r2qr3y1b-btaxuyjcvs.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web.HttpUtility#[.{fingerprint=btaxuyjcvs}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.HttpUtility.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nnwpeck7hq", "Integrity": "k+9yeRoQNPUaO+1j1JqT9iZ8ugHFS5GI9x9Ep7iI/PA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.HttpUtility.wasm", "FileLength": 10060, "LastWriteTime": "2025-07-12T08:24:04.9992281+00:00"}, "9q2IgaHXa1J8MlXBHYsNutTQdcCNwUoHb6txCgqyXBs=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\chv7xdb52w-xfk5e3d2ux.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web#[.{fingerprint=xfk5e3d2ux}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pb8120erd9", "Integrity": "ZcKI1nasaPQvwfogSwQt9MnxZfK+z8zzx1rlpWYgONA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.wasm", "FileLength": 2111, "LastWriteTime": "2025-07-12T08:24:05.0002281+00:00"}, "D9Z0v9U6POpywVzM9erAMDq+bTwlQ+zzGFWmVX6vazU=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\baw1frngq4-dvpjq0fzc1.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Windows#[.{fingerprint=dvpjq0fzc1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nf65zmcbod", "Integrity": "LbXjFbzDuDz4EWalL8cnl+kNMILvcTlbTxokuexZAPw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Windows.wasm", "FileLength": 2270, "LastWriteTime": "2025-07-12T08:24:05.0002281+00:00"}, "G0fa29nZSstuOrKtc3arE/Lw02XqP2EGg48XTAxWcyg=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\4xtso9jv8a-3g0uzfg3q5.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Linq#[.{fingerprint=3g0uzfg3q5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l7xf0yj7r1", "Integrity": "4Q12Wav7WmRi6Rjb6NrSQ8t2W8U9HcAMNxucqwioQnE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Linq.wasm", "FileLength": 2211, "LastWriteTime": "2025-07-12T08:24:05.0012273+00:00"}, "4VbBCWlams9tFXQilDqtTOkAbWfU1VLJx6xo6aa6GZg=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\hteonzcucr-nmw5lay0th.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.ReaderWriter#[.{fingerprint=nmw5lay0th}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g73npthj4u", "Integrity": "BHMfSNFKBB6O7DwOnX6xKlEbf6SU7uO+uaDIVU7naLk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.wasm", "FileLength": 4021, "LastWriteTime": "2025-07-12T08:24:05.0073525+00:00"}, "JqGW/x6t0ZDv7QuJKJHGSzJQmBKnAdmHybohUe5DzyU=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\lb2blsgq0l-35x5v84a2s.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Serialization#[.{fingerprint=35x5v84a2s}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8bjadm0num", "Integrity": "vHrTH+CFn6bTfMXsNxSuyJ4kkSnHsoyuu5JRJMm1uVQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Serialization.wasm", "FileLength": 2244, "LastWriteTime": "2025-07-12T08:24:05.0082265+00:00"}, "+VNunfto/gt5JDhQ0vY3D3BTikNcNEqKY0et/v9+2R0=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\vx78377x5o-dm2u9ur6u8.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XDocument#[.{fingerprint=dm2u9ur6u8}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uk99701wj2", "Integrity": "vg+sRJ8H7b4njJLiDDtGa53X7LJRX5gbspbWt+0IHCA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XDocument.wasm", "FileLength": 2390, "LastWriteTime": "2025-07-12T08:24:05.0092277+00:00"}, "hTrtPl3QP0qN/GUWjf7AxLkZ9D/KMmYz4NGp5knAnfs=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\mfsys91q4c-p77fb7ls9l.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath.XDocument#[.{fingerprint=p77fb7ls9l}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8mbpeu8wxy", "Integrity": "EI1hMmhf8Uh3yeo+mYaw8lzQ/TfpejDkz/+/QVn83PY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.wasm", "FileLength": 2472, "LastWriteTime": "2025-07-12T08:24:05.0337403+00:00"}, "OrceRLc/da0iqO0xK3vrUV2+G9FLkwN8ISO7kmAqJME=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\i03l08g5vi-gkgxk99utf.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath#[.{fingerprint=gkgxk99utf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5dvbhxd7hb", "Integrity": "idivwdEftnBAe8gg6jhgIHJs82XvxyvhjcHZuy7GKKo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.wasm", "FileLength": 2314, "LastWriteTime": "2025-07-12T08:24:05.0377398+00:00"}, "F51vmCiR0870yflpn3ekr4W7DiHhZCbOUs6s44MjkpU=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\cens2en9k9-obea1y0o4e.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlDocument#[.{fingerprint=obea1y0o4e}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z0u359qsy0", "Integrity": "w0ObXpsNkGJELGl9TI3AgPEFM3x81G4KeETL4/bFCEo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlDocument.wasm", "FileLength": 2351, "LastWriteTime": "2025-07-12T08:24:05.040744+00:00"}, "X71J2SkFz8A23Y/nCMWGPUrXd4szcHkiNfOL7PedSTc=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\r8xj4nmt5f-69wknkil2z.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlSerializer#[.{fingerprint=69wknkil2z}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ya84tiro7n", "Integrity": "CngJxBiq4MBqbu6qW3FJg3Gg6fSHVZNcY6G/hSjzYgA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.wasm", "FileLength": 2851, "LastWriteTime": "2025-07-12T08:24:05.0417403+00:00"}, "HC7fratEJ7aW58E8LeI2ZT6uyAy1wmikmQRzwGcE7Dw=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\jehs01rlfm-ob3ozmrjnu.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml#[.{fingerprint=ob3ozmrjnu}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "seg3gdhqeu", "Integrity": "1GkPQrZIRAsULitxmnn4zXS1sFj/OSqV1nMuc5+xFF8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.wasm", "FileLength": 4235, "LastWriteTime": "2025-07-12T08:24:05.0417403+00:00"}, "FeoE9jTwpLDW90Jc6FncH4GgbCmLgNEEMDLB9sEdtQo=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\gujgnu7tzg-n8l4zjq3i2.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System#[.{fingerprint=n8l4zjq3i2}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0fk7qlkmr0", "Integrity": "HPFwWMXgZop84xvtGEdxS4Nmgl7RNcYuNa3cVnu+bZQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.wasm", "FileLength": 11848, "LastWriteTime": "2025-07-12T08:24:05.0437396+00:00"}, "CgdNhQ1YqeIdZKRa74zvT4WDvUEi4G4WxNFBeTGRDBE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\4k0syz9dhx-pe1q66fox7.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/WindowsBase#[.{fingerprint=pe1q66fox7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\WindowsBase.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fznkwt3t1j", "Integrity": "lSWhu5HQGCy0Fsy2AKw4IleG4AA1sJEnYE/JyJtk+o4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\WindowsBase.wasm", "FileLength": 2514, "LastWriteTime": "2025-07-12T08:24:05.0467419+00:00"}, "9mYheCYueUTfu9u0JodcTj/F4HIL9oxM3aV3T1kqMAQ=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\8v6s6t6lkz-ypkr78tz1c.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/mscorlib#[.{fingerprint=ypkr78tz1c}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\mscorlib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w0i5d72psq", "Integrity": "gAyOAO+94sjkLNY68fN4jlxlalciNzJm+I6XYS0I7Bw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\mscorlib.wasm", "FileLength": 14885, "LastWriteTime": "2025-07-12T08:24:05.0767417+00:00"}, "uQMXHakeTOQ+HUQ1a5RE+QmppJEIS2/phuNFsC1nnGE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\7tz9ixl7bw-ifx8vojh9u.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/netstandard#[.{fingerprint=ifx8vojh9u}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\netstandard.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qirbek1i0e", "Integrity": "Eo4bn2RUmuUigS0hb7JarDE3/IB9CSt6TIhSKtfnD8M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\netstandard.wasm", "FileLength": 26237, "LastWriteTime": "2025-07-12T08:24:05.0847424+00:00"}, "7BYPN19KIbzsVhdGd3ocwYmym6W3z/Q0npnr5CgFhHs=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\doncsim87m-gcclii65ud.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.CoreLib#[.{fingerprint=gcclii65ud}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.CoreLib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4fvdxma37a", "Integrity": "UBmSeXdN84hiqIFvJFY//9IleOM9H3ZjuDGYgNFoGSM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.CoreLib.wasm", "FileLength": 1533173, "LastWriteTime": "2025-07-12T08:24:05.5271041+00:00"}, "6Fusrq/5rnHSvHUvLXFLLC/w4n92cRKHftcARghD124=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\bzk53qmj4u-krzbht210g.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet#[.{fingerprint=krzbht210g}]?.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "287x1i8tsp", "Integrity": "LnTpohuyB7qCHmU/gJEz5dlO6IH623+II5JrBSlu8b4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js", "FileLength": 12781, "LastWriteTime": "2025-07-12T08:24:05.5301153+00:00"}, "iL90U+IbX+LCD3Esz6pKJXjcJVnah1WHcYuQo7qrG4E=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\0z7v1j8chv-f3yjzao5wb.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.js#[.{fingerprint=f3yjzao5wb}]?.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ld7tbigvw9", "Integrity": "ZwIDTLCxA+aYBlfUhYxxExurATFmX1TeHYZtp1CM1Mw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js.map", "FileLength": 21303, "LastWriteTime": "2025-07-12T08:24:05.6585506+00:00"}, "nCsjZr5HlJMGpHZHwKU0tB+vZUaUIJSiKxMNgGQWP3A=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\khwrgt1tqw-uhsveqo9bs.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.native#[.{fingerprint=uhsveqo9bs}]!.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g3tuo91ndp", "Integrity": "zJqkY/VaNC9384QQYfnWJavcdCmHHGZYYa4dsGuL1X0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.js", "FileLength": 35022, "LastWriteTime": "2025-07-12T08:24:05.6956308+00:00"}, "XdZxWUHz68YY5nB0E3nHLXGvJH82GHxhPgO3LGl2qIs=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\ffch3q980r-swgexbmoy7.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.native#[.{fingerprint=swgexbmoy7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jha0st9jt2", "Integrity": "F7UN4/a/oyI8Ws0Eh+VySD4xt1Mj5BoOd5Rz1srLy2U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.wasm", "FileLength": 1198975, "LastWriteTime": "2025-07-12T08:24:05.8762244+00:00"}, "DjLXB1tWSPKfuv8WlGGdWG2d+q9PPLI0VBkw2eCk7gE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\j05w3fvpec-5nhp1wfg9b.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime#[.{fingerprint=5nhp1wfg9b}]!.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "temgmv0ap4", "Integrity": "i/ieKoSIsEfcjibSDSUao0clzABnZuwK6zycugy2KUQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js", "FileLength": 56235, "LastWriteTime": "2025-07-12T08:24:06.0043975+00:00"}, "QWzGpk5NEy2vhXNP4AfMygKbluTZaLMxytZkJIpyNrA=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\6fu2tvw3qp-sw8h8rjm4u.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime.js#[.{fingerprint=sw8h8rjm4u}]?.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bopwojh8cb", "Integrity": "+50SWGZnXo43nreZeh39dFiihsE6JvkFMoxdp7GRa3M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js.map", "FileLength": 88603, "LastWriteTime": "2025-07-12T08:24:06.0465654+00:00"}, "Sj3jriOvWgvPgk7SWmj2cGN61+JbprlSdaXUP0nvnkU=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\6o6c7migmt-tjcz0u77k5.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_CJK#[.{fingerprint=tjcz0u77k5}]!.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ffu5aujli6", "Integrity": "Sxt3k51yp5RyINpi7a/YQNnTWexafADiNQYBnBjQrYc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_CJK.dat", "FileLength": 359724, "LastWriteTime": "2025-07-12T08:24:06.0926508+00:00"}, "3r4PsC9UWEH/Od1UkBLZcVDrTz/xe7WoBKo4XMjIInE=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\t20fngc4ms-tptq2av103.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_EFIGS#[.{fingerprint=tptq2av103}]!.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kcp4n5gllp", "Integrity": "rLsEn/DgWXf7nRK2qegv2ARpYrcwixMOaXOoFcVmgjg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "FileLength": 220055, "LastWriteTime": "2025-07-12T08:24:06.1202183+00:00"}, "hazTPLuwg8boUJSxu9Z7SmiKr2giQAItDJuWthvsM1A=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\0tng54hbdz-lfu7j35m59.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_no_CJK#[.{fingerprint=lfu7j35m59}]!.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m3twp0zama", "Integrity": "UsST+ZWYFgogrz0pZB/4gGQWboPgRkurfdpi/0/ENCA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "FileLength": 347023, "LastWriteTime": "2025-07-12T08:24:06.1638271+00:00"}, "Ov2cNqGhe5txY0CaiPGjk5RnygSIv5XenCj//bA5Fmg=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\hpseljw7hx-yxj9ztugam.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/CodingZoo.Shared#[.{fingerprint=yxj9ztugam}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\CodingZoo.Shared.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3i2atglckj", "Integrity": "1QewfGwNu3K+JhLsTWCLnXMumUiIjSWACjiFHpSjvL0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\CodingZoo.Shared.wasm", "FileLength": 18169, "LastWriteTime": "2025-07-12T08:24:06.1668292+00:00"}, "7HP0LKnFj/QjciZv6Eu4KvkWimAaz+UM4lmTYgW7G30=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\l0wgz7us15-2y36r5kz7f.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/CodingZoo.Shared#[.{fingerprint=2y36r5kz7f}]!.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\CodingZoo.Shared.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "40u9g8jfql", "Integrity": "JCB4YwDvCe50e/m24w6rg9KaA/uLfQhS9RxEvMRudFU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\CodingZoo.Shared.pdb", "FileLength": 12133, "LastWriteTime": "2025-07-12T08:24:05.2914407+00:00"}, "ghKPIcR/x0t0POa2lu7aL7UWU68fvQsadNJBXmAmChc=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\ytg5f32da8-7uvcc9n584.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/CodingZoo.Client#[.{fingerprint=7uvcc9n584}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\CodingZoo.Client.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7n9gjmfk5t", "Integrity": "LXCbTkkdr+XBc0OBmAVWfXU2OcHyLhB9EaX/SMUIXzg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\CodingZoo.Client.wasm", "FileLength": 30168, "LastWriteTime": "2025-07-12T08:24:04.9782203+00:00"}, "6OAIe8VVPGxja7QByUl0qcTlYIIqLKS6uLX/cxYwQoM=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\h5viydrc8r-1oi11q0mzf.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/CodingZoo.Client#[.{fingerprint=1oi11q0mzf}]!.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\CodingZoo.Client.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3oqvfp6i8n", "Integrity": "PRQQt3k8xcne5kQQAskdVgRmWcVLyLpe3N4+haMjSVA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\CodingZoo.Client.pdb", "FileLength": 26617, "LastWriteTime": "2025-07-12T08:24:04.9862196+00:00"}, "7TBO5kberbGRVPq4qUOI6owXjA/agm6CjmAA4c+3opQ=": {"Identity": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\80qimy8xy3-g9x3uo891y.gz", "SourceId": "CodingZoo.Client", "SourceType": "Computed", "ContentRoot": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.boot.json.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.boot.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ugf3s8oojy", "Integrity": "Dq3f1NLfo1tulVORkq8SqN9cuECATTCUTSIFHexwokE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "D:\\DotNet\\Blazor\\Wasm\\CodingZoo\\src\\CodingZoo.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.boot.json", "FileLength": 12553, "LastWriteTime": "2025-07-12T08:24:05.0002281+00:00"}}, "CachedCopyCandidates": {}}